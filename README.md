# 🌿 Seaweed Health Classifier

An AI-powered web application for analyzing seaweed health conditions using machine learning and computer vision. Built with TensorFlow.js and Teachable Machine.

![Seaweed Classifier Demo](docs/images/demo-screenshot.png)

## ✨ Features

- **AI-Powered Analysis**: Uses Google's Teachable Machine model for accurate seaweed health classification
- **Real-time Processing**: Instant analysis of uploaded seaweed images
- **Modern UI/UX**: Clean, responsive design with smooth animations
- **Drag & Drop Support**: Easy image upload with drag-and-drop functionality
- **Mobile Friendly**: Fully responsive design that works on all devices
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Progressive Enhancement**: Works even with JavaScript disabled (basic functionality)

## 🚀 Quick Start

### Prerequisites

- Modern web browser with JavaScript enabled
- Internet connection (for loading the AI model)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/seaweed-health-classifier.git
   cd seaweed-health-classifier
   ```

2. **Install dependencies** (optional, for development)
   ```bash
   npm install
   ```

3. **Open the application**
   - **Simple**: Open `index.html` in your web browser
   - **Development**: Run `npm run dev` for live server with hot reload
   - **Production**: Run `npm run serve` for optimized serving

## 📖 Usage

1. **Upload an Image**
   - Click "Choose Image" button or drag and drop an image
   - Supported formats: JPEG, PNG, WebP
   - Maximum file size: 10MB

2. **Analyze**
   - Click "Analyze Health" button
   - Wait for the AI model to process the image

3. **View Results**
   - See confidence percentages for different health conditions
   - Results are sorted by confidence level
   - Color-coded indicators show prediction reliability

## 🏗️ Project Structure

```
seaweed-health-classifier/
├── index.html                 # Main HTML file
├── package.json              # Project configuration
├── README.md                 # This file
├── config/                   # Configuration files
│   ├── app-config.js        # Application settings
│   ├── environment.js       # Environment-specific config
│   └── eslint.config.js     # Code linting rules
├── src/                     # Source code
│   ├── styles/             # CSS stylesheets
│   │   ├── main.css        # Main stylesheet
│   │   ├── base/           # Base styles
│   │   ├── components/     # Component styles
│   │   └── utilities/      # Utility classes
│   ├── scripts/            # JavaScript modules
│   │   ├── main.js         # Application entry point
│   │   ├── model-handler.js # AI model management
│   │   ├── ui-controller.js # UI interactions
│   │   └── utils.js        # Utility functions
│   └── assets/             # Static assets
│       └── images/         # Image files
├── docs/                   # Documentation
│   ├── API.md             # API documentation
│   ├── CONTRIBUTING.md    # Contribution guidelines
│   └── DEPLOYMENT.md      # Deployment guide
└── tests/                 # Test files
    ├── unit/              # Unit tests
    └── integration/       # Integration tests
```

## 🛠️ Development

### Setup Development Environment

1. **Install Node.js** (v14 or higher)
2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run test` - Run tests
- `npm run lint` - Check code quality
- `npm run lint:fix` - Fix linting issues

### Code Style

This project uses ESLint for code quality and follows these conventions:
- ES6+ JavaScript
- 2-space indentation
- Single quotes for strings
- Semicolons required
- Consistent naming conventions

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- model-handler.test.js
```

### Test Structure

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows

## 🚀 Deployment

### Static Hosting

The application can be deployed to any static hosting service:

1. **Netlify**
   - Connect your GitHub repository
   - Set build command: `npm run build`
   - Set publish directory: `dist`

2. **Vercel**
   - Import project from GitHub
   - Vercel will auto-detect settings

3. **GitHub Pages**
   - Enable GitHub Pages in repository settings
   - Select source branch

### Custom Server

For custom deployment, see [DEPLOYMENT.md](docs/DEPLOYMENT.md)

## 🔧 Configuration

### Model Configuration

Update the model URL in `config/app-config.js`:

```javascript
model: {
  url: "https://teachablemachine.withgoogle.com/models/YOUR_MODEL_ID/",
  // ... other settings
}
```

### Environment Variables

Set environment-specific configurations in `config/environment.js`

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](docs/CONTRIBUTING.md) for guidelines.

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google Teachable Machine** - For the machine learning platform
- **TensorFlow.js** - For the JavaScript ML framework
- **Contributors** - Thanks to all who have contributed to this project

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/seaweed-health-classifier/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/seaweed-health-classifier/discussions)

## 🔄 Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and updates.

---

**Made with ❤️ for marine conservation and research**
