# Contributing to Seaweed Health Classifier

Thank you for your interest in contributing to the Seaweed Health Classifier! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

1. **Check existing issues** first to avoid duplicates
2. **Use the issue template** when creating new issues
3. **Provide detailed information** including:
   - Steps to reproduce
   - Expected vs actual behavior
   - <PERSON><PERSON><PERSON> and OS information
   - Screenshots if applicable

### Suggesting Features

1. **Open a feature request** using the appropriate template
2. **Describe the use case** and why it would be valuable
3. **Consider the scope** - keep features focused and manageable
4. **Be open to discussion** about implementation approaches

### Code Contributions

1. **Fork the repository** and create a feature branch
2. **Follow the coding standards** outlined below
3. **Write tests** for new functionality
4. **Update documentation** as needed
5. **Submit a pull request** with a clear description

## 🛠️ Development Setup

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Modern web browser
- Git

### Getting Started

1. **Clone your fork**
   ```bash
   git clone https://github.com/your-username/seaweed-health-classifier.git
   cd seaweed-health-classifier
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Run tests**
   ```bash
   npm test
   ```

### Project Structure

```
src/
├── styles/          # CSS stylesheets
│   ├── base/       # Base styles and variables
│   ├── components/ # Component-specific styles
│   └── utilities/  # Utility classes
├── scripts/        # JavaScript modules
│   ├── main.js     # Application entry point
│   ├── utils.js    # Utility functions
│   └── ...         # Other modules
└── assets/         # Static assets

config/             # Configuration files
docs/              # Documentation
tests/             # Test files
build/             # Build scripts
```

## 📝 Coding Standards

### JavaScript

- **ES6+ syntax** - Use modern JavaScript features
- **Modular architecture** - Keep code organized in modules
- **Consistent naming** - Use camelCase for variables and functions
- **Error handling** - Always handle errors appropriately
- **Documentation** - Add JSDoc comments for functions

**Example:**
```javascript
/**
 * Process image file for analysis
 * @param {File} file - Image file to process
 * @returns {Promise<HTMLImageElement>} Processed image element
 */
async function processImageFile(file) {
  try {
    const validation = validateImageFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }
    
    return await createImageFromFile(file);
  } catch (error) {
    console.error('Image processing failed:', error);
    throw error;
  }
}
```

### CSS

- **BEM methodology** - Use Block Element Modifier naming
- **CSS custom properties** - Use variables for consistency
- **Mobile-first** - Design for mobile, enhance for desktop
- **Semantic classes** - Use meaningful class names

**Example:**
```css
.prediction-item {
  /* Block */
}

.prediction-item__header {
  /* Element */
}

.prediction-item--high-confidence {
  /* Modifier */
}
```

### HTML

- **Semantic markup** - Use appropriate HTML elements
- **Accessibility** - Include ARIA labels and proper structure
- **Performance** - Optimize loading and rendering

## 🧪 Testing

### Writing Tests

- **Unit tests** for individual functions
- **Integration tests** for component interactions
- **Test edge cases** and error conditions
- **Mock external dependencies** appropriately

### Test Structure

```javascript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup before each test
  });

  describe('methodName', () => {
    test('should handle normal case', () => {
      // Test implementation
    });

    test('should handle edge case', () => {
      // Test implementation
    });

    test('should handle error case', () => {
      // Test implementation
    });
  });
});
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- utils.test.js

# Run tests with coverage
npm run test:coverage
```

## 📋 Pull Request Process

### Before Submitting

1. **Ensure tests pass** - All tests should pass
2. **Check code quality** - Run linting and fix issues
3. **Update documentation** - Keep docs current
4. **Test manually** - Verify changes work as expected

### PR Guidelines

1. **Clear title** - Summarize the change concisely
2. **Detailed description** - Explain what and why
3. **Link issues** - Reference related issues
4. **Small, focused changes** - Keep PRs manageable

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

## 🎨 Design Guidelines

### UI/UX Principles

- **Simplicity** - Keep interface clean and intuitive
- **Accessibility** - Ensure usability for all users
- **Performance** - Optimize for speed and responsiveness
- **Consistency** - Maintain visual and interaction patterns

### Color Palette

- Primary: Ocean/seaweed themed colors
- Success: Green tones for positive results
- Warning: Orange/yellow for cautions
- Error: Red tones for errors

### Typography

- Primary font: Poppins
- Hierarchy: Clear heading and body text distinction
- Readability: Appropriate contrast and sizing

## 🐛 Debugging

### Common Issues

1. **Model loading failures**
   - Check network connectivity
   - Verify model URL is accessible
   - Check browser console for errors

2. **Image processing errors**
   - Verify file format support
   - Check file size limits
   - Ensure FileReader API support

3. **UI state issues**
   - Check DOM element availability
   - Verify event listeners are attached
   - Review state management logic

### Debugging Tools

- Browser Developer Tools
- Console logging (development mode)
- Network tab for API calls
- Performance profiler

## 📚 Resources

### Documentation

- [API Documentation](API.md)
- [Deployment Guide](DEPLOYMENT.md)
- [Architecture Overview](ARCHITECTURE.md)

### External Resources

- [TensorFlow.js Documentation](https://www.tensorflow.org/js)
- [Teachable Machine](https://teachablemachine.withgoogle.com/)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- Project documentation

## 📞 Getting Help

- **GitHub Issues** - For bugs and feature requests
- **GitHub Discussions** - For questions and general discussion
- **Code Review** - Request reviews from maintainers

## 📄 License

By contributing, you agree that your contributions will be licensed under the same license as the project (MIT License).

---

Thank you for contributing to the Seaweed Health Classifier! Your efforts help advance marine conservation and research. 🌿
