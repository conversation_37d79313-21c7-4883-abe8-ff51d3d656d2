/* ==========================================================================
   Footer Component Styles
   ========================================================================== */

.footer {
  margin-top: auto;
  padding: var(--spacing-xl) var(--spacing-md);
  text-align: center;
  width: 100%;
}

.footer-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.footer-text a {
  color: var(--color-success);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  position: relative;
}

.footer-text a:hover,
.footer-text a:focus {
  color: var(--color-text-primary);
  text-decoration: underline;
}

.footer-text a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--color-success);
  transition: width var(--transition-normal);
}

.footer-text a:hover::after {
  width: 100%;
}

/* ==========================================================================
   Footer Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .footer {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .footer-text {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .footer {
    padding: var(--spacing-md);
  }
}
