<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Seaweed Health Classifier</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Poppins', sans-serif;
    }

    body {
      background: linear-gradient(270deg, #007F73, #A8CD9F, #627254, #76885B);
      background-size: 800% 800%;
      animation: gradientFlow 20s ease infinite;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30px 20px;
      color: #ffffff;
      text-align: center;
      overflow-x: hidden;
    }

    @keyframes gradientFlow {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    h1 {
      font-size: 2rem;
      max-width: 700px;
      margin-bottom: 30px;
      padding: 15px 25px;
      border-radius: 20px;
      background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(6px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
      line-height: 1.6;
      transition: transform 0.3s ease-in-out;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    h1:hover {
      transform: scale(1.02);
    }

    #fileInput {
      margin: 20px 0;
      padding: 12px 20px;
      border-radius: 12px;
      border: 2px solid #fff;
      background-color: rgba(255, 255, 255, 0.95);
      color: #333;
      font-weight: 500;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    #fileInput:hover {
      background-color: #e2e2e2;
      border-color: #ccc;
    }

    #image-preview {
      max-width: 90vw;
      width: 320px;
      max-height: 320px;
      object-fit: cover;
      border-radius: 20px;
      margin: 25px 0;
      display: none;
      box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
      transition: transform 0.4s ease;
      border: 5px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(2px);
    }

    #image-preview:hover {
      transform: scale(1.06);
    }

    button {
      background: linear-gradient(to right, #003C43, #135D66);
      color: #fff;
      border: none;
      padding: 14px 30px;
      border-radius: 35px;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
      letter-spacing: 1px;
    }

    button:hover {
      background: linear-gradient(to right, #135D66, #1B4242);
      transform: scale(1.05);
    }

    #label-container {
      margin-top: 30px;
      background-color: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 15px;
      max-width: 450px;
      width: 90%;
      backdrop-filter: blur(8px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }

    #label-container div {
      font-size: 1.1rem;
      margin: 12px 0;
      color: #fff;
      font-weight: 500;
      position: relative;
      padding: 10px 15px;
      border-radius: 10px;
      background: rgba(0, 0, 0, 0.25);
      backdrop-filter: blur(4px);
      box-shadow: inset 0 0 8px rgba(255, 255, 255, 0.2);
    }

    #label-container div::after {
      content: "";
      display: block;
      height: 6px;
      width: calc(var(--prob, 0) * 100%);
      background: #00ffbf;
      margin-top: 6px;
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    @media (max-width: 600px) {
      h1 {
        font-size: 1.5rem;
        padding: 10px 15px;
      }

      button, #fileInput {
        width: 100%;
      }

      #label-container div {
        font-size: 1rem;
      }
    }
  </style>
</head>
<body>

  <h1>🌿 Seaweed Condition Classifier</h1>
  
  <input type="file" accept="image/*" id="fileInput" />
  <br />
  <img id="image-preview" />
  <br />
  <button onclick="predictImage()">START</button>
  <div id="label-container"></div>

  <!-- Teachable Machine -->
  <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.9.0"></script>
  <script src="https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8.4/dist/teachablemachine-image.min.js"></script>

  <script>
    const URL = "https://teachablemachine.withgoogle.com/models/SU3lmOLsW/"; // keep your model link
    let model, imageElement;
    const fileInput = document.getElementById("fileInput");
    const imagePreview = document.getElementById("image-preview");
    const labelContainer = document.getElementById("label-container");

    // Load model when page loads
    async function loadModel() {
      const modelURL = URL + "model.json";
      const metadataURL = URL + "metadata.json";
      model = await tmImage.load(modelURL, metadataURL);
    }

    window.onload = async () => {
      await loadModel();
    };

    // Load image
    fileInput.addEventListener("change", (event) => {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        imagePreview.src = e.target.result;
        imagePreview.style.display = "block";
        imageElement = new Image();
        imageElement.src = e.target.result;
      };
      reader.readAsDataURL(file);
    });

    // Predict
    async function predictImage() {
      if (!imageElement || !model) return;

      await new Promise((resolve) => {
        if (imageElement.complete) resolve();
        else imageElement.onload = resolve;
      });

      const prediction = await model.predict(imageElement);
      prediction.sort((a, b) => b.probability - a.probability);

      labelContainer.innerHTML = "";

      prediction.forEach(p => {
        const percent = (p.probability * 100).toFixed(1);
        labelContainer.innerHTML += `<div>${p.className}: ${percent}%</div>`;
      });
    }
  </script>
</body>
</html>
