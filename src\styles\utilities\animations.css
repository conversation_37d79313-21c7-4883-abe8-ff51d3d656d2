/* ==========================================================================
   Animation Utilities
   ========================================================================== */

/* ==========================================================================
   Keyframe Animations
   ========================================================================== */

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes wobble {
  0% { transform: translate3d(0, 0, 0); }
  15% { transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }
  30% { transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }
  45% { transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }
  60% { transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }
  75% { transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }
  100% { transform: translate3d(0, 0, 0); }
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

/* ==========================================================================
   Animation Classes
   ========================================================================== */

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-scale-out {
  animation: scaleOut 0.3s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-bounce {
  animation: bounce 1s;
}

.animate-wobble {
  animation: wobble 1s;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* ==========================================================================
   Animation Delays
   ========================================================================== */

.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-1000 { animation-delay: 1s; }

/* ==========================================================================
   Animation Durations
   ========================================================================== */

.animate-duration-75 { animation-duration: 75ms; }
.animate-duration-100 { animation-duration: 100ms; }
.animate-duration-150 { animation-duration: 150ms; }
.animate-duration-200 { animation-duration: 200ms; }
.animate-duration-300 { animation-duration: 300ms; }
.animate-duration-500 { animation-duration: 500ms; }
.animate-duration-700 { animation-duration: 700ms; }
.animate-duration-1000 { animation-duration: 1000ms; }

/* ==========================================================================
   Animation States
   ========================================================================== */

.animate-paused {
  animation-play-state: paused;
}

.animate-running {
  animation-play-state: running;
}

.animate-infinite {
  animation-iteration-count: infinite;
}

.animate-once {
  animation-iteration-count: 1;
}

.animate-twice {
  animation-iteration-count: 2;
}

/* ==========================================================================
   Hover Animations
   ========================================================================== */

.hover-scale:hover {
  transform: scale(1.05);
  transition: transform var(--transition-normal);
}

.hover-scale-sm:hover {
  transform: scale(1.02);
  transition: transform var(--transition-normal);
}

.hover-scale-lg:hover {
  transform: scale(1.1);
  transition: transform var(--transition-normal);
}

.hover-rotate:hover {
  transform: rotate(5deg);
  transition: transform var(--transition-normal);
}

.hover-float:hover {
  transform: translateY(-5px);
  transition: transform var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(0, 255, 191, 0.5);
  transition: box-shadow var(--transition-normal);
}

/* ==========================================================================
   Reduced Motion Support
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .animate-fade-in,
  .animate-scale-in,
  .animate-slide-in-up,
  .animate-slide-in-down,
  .animate-slide-in-left,
  .animate-slide-in-right {
    animation: none;
    opacity: 1;
    transform: none;
  }
}
