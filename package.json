{"name": "seaweed-health-classifier", "version": "1.0.0", "description": "AI-powered seaweed health classification system using TensorFlow.js and Teachable Machine", "main": "index.html", "scripts": {"dev": "live-server --port=3000 --open=/index.html", "build": "npm run build:css && npm run build:js", "build:css": "postcss src/styles/main.css -o dist/styles/main.min.css --use autoprefixer cssnano", "build:js": "terser src/scripts/*.js -o dist/scripts/main.min.js --compress --mangle", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/scripts/**/*.js", "lint:fix": "eslint src/scripts/**/*.js --fix", "serve": "http-server -p 3000 -o", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["seaweed", "machine-learning", "tensorflow", "image-classification", "teachable-machine", "ai", "health-monitoring"], "author": "Seaweed Health Team", "license": "MIT", "devDependencies": {"autoprefixer": "^10.4.14", "cssnano": "^6.0.1", "eslint": "^8.44.0", "http-server": "^14.1.1", "jest": "^29.6.1", "live-server": "^1.2.2", "postcss": "^8.4.24", "postcss-cli": "^10.1.0", "rimraf": "^5.0.1", "terser": "^5.19.2"}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}}