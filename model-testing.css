/* ==========================================================================
   Seaweed Health Model Testing - Professional Stylesheet
   ========================================================================== */

/* ==========================================================================
   CSS Reset and Variables
   ========================================================================== */

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

:root {
  /* Colors - Seaweed Theme */
  --color-primary: #007F73;
  --color-primary-light: #00A693;
  --color-primary-dark: #005A52;
  --color-secondary: #A8CD9F;
  --color-accent: #00ffbf;

  /* Chart Colors */
  --color-healthy: #00ffbf;
  --color-noise: #ff6b6b;
  --color-ice-ice: #4ecdc4;
  --color-siren-siren: #45b7d1;
  --color-combined: #f9ca24;

  /* UI Colors */
  --color-success: #00C851;
  --color-warning: #ffbb33;
  --color-error: #ff4444;
  --color-info: #33b5e5;

  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-muted: rgba(255, 255, 255, 0.6);
  --color-text-dark: #2c3e50;

  /* Background Colors */
  --color-bg-primary: #1a1a2e;
  --color-bg-secondary: #16213e;
  --color-bg-tertiary: #0f3460;
  --color-bg-card: rgba(255, 255, 255, 0.05);
  --color-bg-card-hover: rgba(255, 255, 255, 0.08);

  /* Border Colors */
  --color-border: rgba(255, 255, 255, 0.1);
  --color-border-light: rgba(255, 255, 255, 0.2);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #007F73, #00A693, #A8CD9F);
  --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02));
  --gradient-button: linear-gradient(135deg, #007F73, #00A693);

  /* Typography */
  --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 2.5rem;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(0, 255, 191, 0.3);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ==========================================================================
   Base Styles
   ========================================================================== */

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-text-primary);
  background: var(--color-bg-primary);
  min-height: 100vh;
  overflow-x: hidden;
}

/* ==========================================================================
   Layout Components
   ========================================================================== */

.main {
  min-height: 100vh;
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* ==========================================================================
   Header Styles
   ========================================================================== */

.header {
  background: var(--gradient-primary);
  padding: var(--spacing-xl) 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header__container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.header__title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header__icon {
  font-size: 1.2em;
  animation: pulse 2s ease-in-out infinite;
}

.header__subtitle {
  font-size: var(--font-size-lg);
  font-weight: 400;
  opacity: 0.9;
  margin: 0;
}

/* ==========================================================================
   Control Panel Styles
   ========================================================================== */

.control-panel {
  background: var(--gradient-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
}

.control-panel__container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  align-items: end;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.control-label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control-input,
.control-select {
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
}

.control-input:focus,
.control-select:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(0, 255, 191, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.control-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  min-height: 44px;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn--primary {
  background: var(--gradient-button);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-md);
}

.btn--primary:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-light);
}

.btn--secondary:not(:disabled):hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.btn--tertiary {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
}

.btn--tertiary:not(:disabled):hover {
  color: var(--color-text-primary);
  border-color: var(--color-border-light);
  background: rgba(255, 255, 255, 0.05);
}

/* ==========================================================================
   Metrics Section
   ========================================================================== */

.metrics-section {
  margin: var(--spacing-xl) 0;
}

.metrics-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.metric-card {
  background: var(--gradient-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
}

.metric-card:hover {
  background: var(--color-bg-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-icon {
  font-size: var(--font-size-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 255, 191, 0.1);
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.metric-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1.2;
}

/* ==========================================================================
   Results Section
   ========================================================================== */

.results-section {
  margin: var(--spacing-xl) 0;
}

.results-container {
  background: var(--gradient-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
}

.results-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.result-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--color-accent);
  transition: width var(--transition-normal);
}

.result-card:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.result-card:hover::before {
  width: 8px;
}

.result-card[data-condition="healthy"]::before {
  background: var(--color-healthy);
}

.result-card[data-condition="noise"]::before {
  background: var(--color-noise);
}

.result-card[data-condition="ice-ice"]::before {
  background: var(--color-ice-ice);
}

.result-card[data-condition="siren-siren"]::before {
  background: var(--color-siren-siren);
}

.result-card[data-condition="combined"]::before {
  background: var(--color-combined);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.result-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.result-status {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-status--good {
  background: rgba(0, 200, 81, 0.2);
  color: var(--color-success);
  border: 1px solid rgba(0, 200, 81, 0.3);
}

.result-status--warning {
  background: rgba(255, 187, 51, 0.2);
  color: var(--color-warning);
  border: 1px solid rgba(255, 187, 51, 0.3);
}

.result-status--error {
  background: rgba(255, 68, 68, 0.2);
  color: var(--color-error);
  border: 1px solid rgba(255, 68, 68, 0.3);
}

.result-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.result-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
}

.result-metric-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: 500;
}

.result-metric-value {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-text-primary);
}

/* ==========================================================================
   Progress Section
   ========================================================================== */

.progress-section {
  margin: var(--spacing-xl) 0;
}

.progress-container {
  background: var(--gradient-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.progress-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.progress-percentage {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-accent);
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-accent), var(--color-primary));
  border-radius: var(--radius-md);
  transition: width 0.3s ease;
  position: relative;
  width: 0%;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.progress-info {
  text-align: center;
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* ==========================================================================
   Footer Styles
   ========================================================================== */

.footer {
  background: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border);
  padding: var(--spacing-xl) 0;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  text-align: center;
}

.footer-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin: 0;
}

.footer-text a {
  color: var(--color-accent);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.footer-text a:hover {
  color: var(--color-text-primary);
  text-decoration: underline;
}

/* ==========================================================================
   Additional Animations
   ========================================================================== */

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 255, 191, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 255, 191, 0.6);
  }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ==========================================================================
   Focus Management
   ========================================================================== */

*:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
  .control-panel,
  .footer {
    display: none;
  }

  .chart-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  body {
    background: white;
    color: black;
  }
}

.btn__loader {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn__loader::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ==========================================================================
   Charts Section
   ========================================================================== */

.charts-section {
  display: grid;
  gap: var(--spacing-xl);
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
}

.chart-card {
  background: var(--gradient-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.chart-card:hover {
  background: var(--color-bg-card-hover);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.chart-card--main {
  grid-column: 1 / -1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.chart-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.chart-control {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-control:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--color-text-primary);
  transform: scale(1.05);
}

.chart-wrapper {
  position: relative;
  height: 400px;
  margin-bottom: var(--spacing-lg);
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  justify-content: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
}

.legend-label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-secondary);
}

/* ==========================================================================
   Animations
   ========================================================================== */

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (min-width: 768px) {
  .charts-container {
    grid-template-columns: 1fr 1fr;
  }

  .chart-card--main {
    grid-column: 1 / -1;
  }
}

@media (min-width: 1024px) {
  .control-panel__container {
    grid-template-columns: repeat(4, 1fr);
  }

  .control-actions {
    grid-column: 1 / -1;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .main {
    padding: var(--spacing-md);
    gap: var(--spacing-lg);
  }

  .header__title {
    font-size: var(--font-size-2xl);
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .control-panel__container {
    grid-template-columns: 1fr;
  }

  .control-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .chart-wrapper {
    height: 300px;
  }

  .chart-legend {
    flex-direction: column;
    align-items: center;
  }
}
