#!/usr/bin/env node

/**
 * Build Script
 * Seaweed Health Classifier
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Build configuration
const config = {
  srcDir: 'src',
  distDir: 'dist',
  configDir: 'config',
  docsDir: 'docs',
  assetsDir: 'src/assets',
  clean: true,
  minify: true,
  sourceMaps: false
};

/**
 * Logger utility
 */
const logger = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Ensure directory exists
 */
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    logger.info(`Created directory: ${dir}`);
  }
}

/**
 * Clean build directory
 */
function clean() {
  if (fs.existsSync(config.distDir)) {
    fs.rmSync(config.distDir, { recursive: true, force: true });
    logger.info(`Cleaned ${config.distDir}`);
  }
}

/**
 * Copy file with optional transformation
 */
function copyFile(src, dest, transform = null) {
  ensureDir(path.dirname(dest));
  
  let content = fs.readFileSync(src, 'utf8');
  
  if (transform) {
    content = transform(content);
  }
  
  fs.writeFileSync(dest, content);
  logger.info(`Copied: ${src} → ${dest}`);
}

/**
 * Copy directory recursively
 */
function copyDir(src, dest) {
  if (!fs.existsSync(src)) {
    logger.warn(`Source directory not found: ${src}`);
    return;
  }
  
  ensureDir(dest);
  
  const items = fs.readdirSync(src);
  
  items.forEach(item => {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);
    
    if (fs.statSync(srcPath).isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      copyFile(srcPath, destPath);
    }
  });
}

/**
 * Minify CSS using cssnano
 */
function minifyCSS(content) {
  if (!config.minify) return content;
  
  try {
    // Simple CSS minification (remove comments and extra whitespace)
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/;\s*}/g, '}') // Remove last semicolon in blocks
      .replace(/\s*{\s*/g, '{') // Clean braces
      .replace(/;\s*/g, ';') // Clean semicolons
      .trim();
  } catch (error) {
    logger.warn(`CSS minification failed: ${error.message}`);
    return content;
  }
}

/**
 * Minify JavaScript using basic techniques
 */
function minifyJS(content) {
  if (!config.minify) return content;
  
  try {
    // Basic JS minification (remove comments and extra whitespace)
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
      .replace(/\/\/.*$/gm, '') // Remove line comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/;\s*}/g, '}') // Clean semicolons before braces
      .replace(/\s*{\s*/g, '{') // Clean braces
      .replace(/;\s*/g, ';') // Clean semicolons
      .trim();
  } catch (error) {
    logger.warn(`JS minification failed: ${error.message}`);
    return content;
  }
}

/**
 * Process HTML file
 */
function processHTML(content) {
  // Update paths for production
  content = content.replace(/src\//g, '');
  content = content.replace(/config\//g, '');
  
  if (config.minify) {
    // Basic HTML minification
    content = content
      .replace(/>\s+</g, '><') // Remove whitespace between tags
      .replace(/\s+/g, ' ') // Collapse whitespace
      .trim();
  }
  
  return content;
}

/**
 * Build CSS files
 */
function buildCSS() {
  logger.info('Building CSS...');
  
  const cssFiles = [
    'src/styles/main.css'
  ];
  
  cssFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join(config.distDir, 'styles', path.basename(file));
      copyFile(file, destFile, minifyCSS);
    }
  });
  
  // Copy all CSS files from styles directory
  if (fs.existsSync('src/styles')) {
    copyDir('src/styles', path.join(config.distDir, 'styles'));
  }
}

/**
 * Build JavaScript files
 */
function buildJS() {
  logger.info('Building JavaScript...');
  
  const jsFiles = [
    'src/scripts/utils.js',
    'src/scripts/model-handler.js',
    'src/scripts/ui-controller.js',
    'src/scripts/main.js'
  ];
  
  jsFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join(config.distDir, 'scripts', path.basename(file));
      copyFile(file, destFile, minifyJS);
    }
  });
  
  // Copy config files
  if (fs.existsSync('config')) {
    copyDir('config', config.distDir);
  }
}

/**
 * Build HTML files
 */
function buildHTML() {
  logger.info('Building HTML...');
  
  const htmlFiles = ['index.html'];
  
  htmlFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const destFile = path.join(config.distDir, file);
      copyFile(file, destFile, processHTML);
    }
  });
}

/**
 * Copy assets
 */
function copyAssets() {
  logger.info('Copying assets...');
  
  if (fs.existsSync(config.assetsDir)) {
    copyDir(config.assetsDir, path.join(config.distDir, 'assets'));
  }
  
  // Copy other important files
  const otherFiles = [
    'package.json',
    'README.md',
    'LICENSE'
  ];
  
  otherFiles.forEach(file => {
    if (fs.existsSync(file)) {
      copyFile(file, path.join(config.distDir, file));
    }
  });
}

/**
 * Generate build info
 */
function generateBuildInfo() {
  const buildInfo = {
    version: require('../package.json').version,
    buildDate: new Date().toISOString(),
    environment: 'production',
    minified: config.minify,
    sourceMaps: config.sourceMaps
  };
  
  fs.writeFileSync(
    path.join(config.distDir, 'build-info.json'),
    JSON.stringify(buildInfo, null, 2)
  );
  
  logger.info('Generated build info');
}

/**
 * Main build function
 */
function build() {
  const startTime = Date.now();
  
  logger.info('Starting build process...');
  
  try {
    // Clean previous build
    if (config.clean) {
      clean();
    }
    
    // Ensure dist directory exists
    ensureDir(config.distDir);
    
    // Build steps
    buildCSS();
    buildJS();
    buildHTML();
    copyAssets();
    generateBuildInfo();
    
    const buildTime = Date.now() - startTime;
    logger.success(`Build completed in ${buildTime}ms`);
    logger.info(`Output directory: ${config.distDir}`);
    
  } catch (error) {
    logger.error(`Build failed: ${error.message}`);
    process.exit(1);
  }
}

// Run build if called directly
if (require.main === module) {
  build();
}

module.exports = { build, config };
