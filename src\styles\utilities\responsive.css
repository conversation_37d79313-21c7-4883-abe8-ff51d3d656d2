/* ==========================================================================
   Responsive Utilities
   ========================================================================== */

/* ==========================================================================
   Breakpoint Variables
   ========================================================================== */

:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ==========================================================================
   Container Queries
   ========================================================================== */

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* ==========================================================================
   Display Utilities
   ========================================================================== */

/* Mobile First - Hidden by default on mobile */
.hidden-xs { display: none; }
.hidden-sm { display: none; }

@media (min-width: 480px) {
  .hidden-xs { display: block; }
  .visible-xs { display: none; }
}

@media (min-width: 640px) {
  .hidden-sm { display: block; }
  .visible-sm { display: none; }
}

@media (min-width: 768px) {
  .hidden-md { display: none; }
  .visible-md { display: block; }
}

@media (min-width: 1024px) {
  .hidden-lg { display: none; }
  .visible-lg { display: block; }
}

@media (min-width: 1280px) {
  .hidden-xl { display: none; }
  .visible-xl { display: block; }
}

/* ==========================================================================
   Spacing Utilities - Responsive
   ========================================================================== */

/* Mobile spacing */
.p-mobile { padding: var(--spacing-sm); }
.px-mobile { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.py-mobile { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.m-mobile { margin: var(--spacing-sm); }
.mx-mobile { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.my-mobile { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }

/* Tablet spacing */
@media (min-width: 768px) {
  .p-tablet { padding: var(--spacing-md); }
  .px-tablet { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
  .py-tablet { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
  .m-tablet { margin: var(--spacing-md); }
  .mx-tablet { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
  .my-tablet { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
}

/* Desktop spacing */
@media (min-width: 1024px) {
  .p-desktop { padding: var(--spacing-lg); }
  .px-desktop { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
  .py-desktop { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
  .m-desktop { margin: var(--spacing-lg); }
  .mx-desktop { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
  .my-desktop { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
}

/* ==========================================================================
   Typography - Responsive
   ========================================================================== */

.text-responsive {
  font-size: var(--font-size-sm);
}

@media (min-width: 640px) {
  .text-responsive {
    font-size: var(--font-size-base);
  }
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: var(--font-size-lg);
  }
}

@media (min-width: 1024px) {
  .text-responsive {
    font-size: var(--font-size-xl);
  }
}

.heading-responsive {
  font-size: var(--font-size-xl);
}

@media (min-width: 640px) {
  .heading-responsive {
    font-size: var(--font-size-2xl);
  }
}

@media (min-width: 768px) {
  .heading-responsive {
    font-size: var(--font-size-3xl);
  }
}

@media (min-width: 1024px) {
  .heading-responsive {
    font-size: var(--font-size-4xl);
  }
}

/* ==========================================================================
   Layout - Responsive
   ========================================================================== */

.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
    gap: var(--spacing-lg);
  }
}

.grid-responsive {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
  }
}

/* ==========================================================================
   Component Responsive Overrides
   ========================================================================== */

/* Button responsive */
@media (max-width: 767px) {
  .btn-responsive {
    width: 100%;
    max-width: 300px;
  }
}

/* Card responsive */
.card-responsive {
  width: 100%;
  max-width: 400px;
}

@media (min-width: 768px) {
  .card-responsive {
    max-width: 500px;
  }
}

@media (min-width: 1024px) {
  .card-responsive {
    max-width: 600px;
  }
}

/* Image responsive */
.img-responsive {
  width: 100%;
  height: auto;
  max-width: 280px;
}

@media (min-width: 640px) {
  .img-responsive {
    max-width: 320px;
  }
}

@media (min-width: 768px) {
  .img-responsive {
    max-width: 400px;
  }
}

/* ==========================================================================
   Accessibility - Responsive
   ========================================================================== */

/* Larger touch targets on mobile */
@media (max-width: 767px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Reduced motion on mobile for better performance */
@media (max-width: 767px) and (prefers-reduced-motion: no-preference) {
  .animate-mobile-safe {
    animation-duration: 0.2s;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast {
    border: 2px solid currentColor;
  }
}
