/* ==========================================================================
   Upload Component Styles
   ========================================================================== */

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  width: 100%;
  max-width: 500px;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl) var(--spacing-3xl);
  border: 3px dashed var(--color-primary-light);
  border-radius: var(--radius-2xl);
  background: var(--gradient-card);
  color: var(--color-text-dark);
  cursor: pointer;
  transition: all var(--transition-normal);
  min-width: 320px;
  min-height: 200px;
  text-align: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-lg);
}

.upload-label::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
}

.upload-label:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.upload-label:hover::after {
  opacity: 1;
}

.upload-label:focus-within {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.upload-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  position: relative;
  z-index: 1;
}

.upload-hint {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  opacity: 0.8;
  position: relative;
  z-index: 1;
}

.upload-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Upload states */
.upload-label.dragover {
  background: rgba(0, 127, 115, 0.1);
  border-color: var(--color-primary);
  transform: scale(1.02);
}

.upload-label.has-file {
  background: rgba(0, 255, 191, 0.1);
  border-color: var(--color-success);
}

.upload-label.has-file .upload-text {
  color: var(--color-success);
}

.upload-label.error {
  background: rgba(244, 67, 54, 0.1);
  border-color: var(--color-error);
}

.upload-label.error .upload-text {
  color: var(--color-error);
}

/* Enhanced Upload icon */
.upload-label::before {
  content: '📁';
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  display: block;
  transition: all var(--transition-normal);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 1;
}

.upload-label:hover::before {
  transform: scale(1.15) rotate(5deg);
}

.upload-label.has-file::before {
  content: '✅';
  animation: bounce 0.6s ease;
}

.upload-label.error::before {
  content: '❌';
  animation: shake 0.5s ease;
}

/* ==========================================================================
   Upload Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .upload-label {
    min-width: unset;
    width: 100%;
    max-width: 400px;
    padding: var(--spacing-lg) var(--spacing-xl);
  }
  
  .upload-text {
    font-size: var(--font-size-base);
  }
  
  .upload-hint {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .upload-label {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .upload-label::before {
    font-size: var(--font-size-xl);
  }
}
