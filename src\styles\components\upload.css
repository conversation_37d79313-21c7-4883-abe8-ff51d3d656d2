/* ==========================================================================
   Upload Component Styles
   ========================================================================== */

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  width: 100%;
  max-width: 500px;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl) var(--spacing-3xl);
  border: 3px dashed var(--color-primary-light);
  border-radius: var(--radius-2xl);
  background: var(--gradient-card);
  color: var(--color-text-dark);
  cursor: pointer;
  transition: all var(--transition-normal);
  min-width: 320px;
  min-height: 200px;
  text-align: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-lg);
}

.upload-label::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
}

.upload-label:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.upload-label:hover::after {
  opacity: 1;
}

.upload-label:focus-within {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.upload-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  position: relative;
  z-index: 1;
}

.upload-hint {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  opacity: 0.8;
  position: relative;
  z-index: 1;
}

.upload-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Upload progress indicator */
.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  overflow: hidden;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.upload-progress.visible {
  opacity: 1;
}

.upload-progress-bar {
  height: 100%;
  background: var(--gradient-success);
  width: 0%;
  transition: width 0.3s ease;
  position: relative;
}

.upload-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

/* File validation messages */
.upload-message {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  opacity: 0;
  transition: all var(--transition-normal);
  pointer-events: none;
  white-space: nowrap;
  z-index: 10;
}

.upload-message.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.upload-message.success {
  background: var(--color-success);
  color: white;
  box-shadow: var(--shadow-success);
}

.upload-message.error {
  background: var(--color-error);
  color: white;
  box-shadow: var(--shadow-error);
}

.upload-message.info {
  background: var(--color-info);
  color: white;
  box-shadow: var(--shadow-md);
}

/* Enhanced Upload states */
.upload-label.dragover {
  background: var(--gradient-glass);
  border-color: var(--color-primary);
  border-width: 4px;
  transform: translateY(-2px) scale(1.03);
  box-shadow: var(--shadow-glow);
  animation: dragPulse 0.6s ease-in-out infinite alternate;
}

.upload-label.dragover::after {
  opacity: 1;
  background: linear-gradient(135deg, rgba(0, 107, 93, 0.1), rgba(78, 205, 196, 0.1));
}

.upload-label.has-file {
  background: var(--gradient-card);
  border-color: var(--color-success);
  border-width: 3px;
  box-shadow: var(--shadow-success);
}

.upload-label.has-file .upload-text {
  color: var(--color-success-dark);
  font-weight: var(--font-weight-bold);
}

.upload-label.has-file .upload-hint {
  color: var(--color-success);
  opacity: 1;
}

.upload-label.has-file {
  animation: uploadSuccess 0.6s ease-out;
}

.upload-label.error {
  background: var(--gradient-card);
  border-color: var(--color-error);
  border-width: 3px;
  box-shadow: var(--shadow-error);
  animation: shake 0.5s ease-in-out;
}

.upload-label.error .upload-text {
  color: var(--color-error-dark);
  font-weight: var(--font-weight-bold);
}

.upload-label.error .upload-hint {
  color: var(--color-error);
  opacity: 1;
}

/* Enhanced Upload icon */
.upload-label::before {
  content: '📁';
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  display: block;
  transition: all var(--transition-normal);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 1;
}

.upload-label:hover::before {
  transform: scale(1.15) rotate(5deg);
}

.upload-label.has-file::before {
  content: '✅';
  animation: bounce 0.6s ease;
}

.upload-label.error::before {
  content: '❌';
  animation: shake 0.5s ease;
}

/* ==========================================================================
   Upload Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .upload-label {
    min-width: unset;
    width: 100%;
    max-width: 400px;
    padding: var(--spacing-lg) var(--spacing-xl);
  }
  
  .upload-text {
    font-size: var(--font-size-base);
  }
  
  .upload-hint {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .upload-label {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .upload-label::before {
    font-size: var(--font-size-xl);
  }
}

/* ==========================================================================
   Enhanced Upload Animations
   ========================================================================== */

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0) scale(1);
  }
  40%, 43% {
    transform: translate3d(0, -12px, 0) scale(1.05);
  }
  70% {
    transform: translate3d(0, -6px, 0) scale(1.02);
  }
  90% {
    transform: translate3d(0, -2px, 0) scale(1.01);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-6px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(6px);
  }
}

@keyframes dragPulse {
  0% {
    box-shadow: var(--shadow-glow);
    border-color: var(--color-primary);
  }
  100% {
    box-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
    border-color: var(--color-accent);
  }
}

@keyframes uploadSuccess {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}
