/**
 * Test Setup
 * Global test configuration and utilities
 */

// Mock DOM environment
const { JSD<PERSON> } = require('jsdom');

const dom = new JSDOM(`
<!DOCTYPE html>
<html>
<head>
  <title>Test Environment</title>
</head>
<body>
  <input type="file" id="fileInput" />
  <img id="image-preview" />
  <button id="predict-button">Predict</button>
  <button id="reset-button">Reset</button>
  <div id="results-container"></div>
  <div id="predictions-list"></div>
  <div id="loading-indicator"></div>
  <div id="error-container"></div>
  <div id="error-message"></div>
  <button id="retry-button">Retry</button>
  <label class="upload-label"></label>
</body>
</html>
`, {
  url: 'http://localhost:3000',
  pretendToBeVisual: true,
  resources: 'usable'
});

// Set up global DOM
global.window = dom.window;
global.document = dom.window.document;
global.navigator = dom.window.navigator;
global.HTMLElement = dom.window.HTMLElement;
global.HTMLImageElement = dom.window.HTMLImageElement;
global.File = dom.window.File;
global.FileReader = dom.window.FileReader;
global.Image = dom.window.Image;
global.CustomEvent = dom.window.CustomEvent;

// Mock console methods for testing
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  group: jest.fn(),
  groupEnd: jest.fn()
};

// Mock TensorFlow.js and Teachable Machine
global.tmImage = {
  load: jest.fn().mockResolvedValue({
    predict: jest.fn().mockResolvedValue([
      { className: 'Healthy', probability: 0.85 },
      { className: 'Diseased', probability: 0.15 }
    ]),
    dispose: jest.fn()
  })
};

// Mock fetch for network requests
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve('')
  })
);

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now()),
  timing: {
    navigationStart: Date.now() - 1000,
    loadEventEnd: Date.now()
  },
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000
  }
};

// Mock URL.createObjectURL
global.URL = {
  createObjectURL: jest.fn(() => 'blob:mock-url'),
  revokeObjectURL: jest.fn()
};

// Load application configuration
require('../config/app-config.js');

// Test utilities
global.TestUtils = {
  /**
   * Create mock file
   */
  createMockFile(name = 'test.jpg', type = 'image/jpeg', size = 1024) {
    const file = new File(['mock content'], name, { type });
    Object.defineProperty(file, 'size', { value: size });
    return file;
  },

  /**
   * Create mock image element
   */
  createMockImage(src = 'data:image/jpeg;base64,mock', width = 320, height = 240) {
    const img = new Image();
    img.src = src;
    img.width = width;
    img.height = height;
    img.complete = true;
    img.naturalWidth = width;
    img.naturalHeight = height;
    return img;
  },

  /**
   * Wait for next tick
   */
  nextTick() {
    return new Promise(resolve => setTimeout(resolve, 0));
  },

  /**
   * Wait for specified time
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Trigger event on element
   */
  triggerEvent(element, eventType, eventData = {}) {
    const event = new CustomEvent(eventType, { detail: eventData });
    element.dispatchEvent(event);
  },

  /**
   * Mock FileReader
   */
  mockFileReader(result = 'data:image/jpeg;base64,mock') {
    const mockReader = {
      readAsDataURL: jest.fn(function() {
        setTimeout(() => {
          this.result = result;
          if (this.onload) this.onload({ target: this });
        }, 0);
      }),
      result: null,
      onload: null,
      onerror: null
    };
    
    global.FileReader = jest.fn(() => mockReader);
    return mockReader;
  },

  /**
   * Reset all mocks
   */
  resetMocks() {
    jest.clearAllMocks();
    
    // Reset console mocks
    console.log.mockClear();
    console.warn.mockClear();
    console.error.mockClear();
    console.info.mockClear();
    
    // Reset fetch mock
    fetch.mockClear();
    
    // Reset TensorFlow mocks
    if (global.tmImage) {
      global.tmImage.load.mockClear();
    }
  },

  /**
   * Setup DOM for test
   */
  setupDOM() {
    document.body.innerHTML = `
      <input type="file" id="fileInput" />
      <img id="image-preview" />
      <button id="predict-button">Predict</button>
      <button id="reset-button">Reset</button>
      <div id="results-container"></div>
      <div id="predictions-list"></div>
      <div id="loading-indicator"></div>
      <div id="error-container"></div>
      <div id="error-message"></div>
      <button id="retry-button">Retry</button>
      <label class="upload-label"></label>
    `;
  }
};

// Setup before each test
beforeEach(() => {
  global.TestUtils.resetMocks();
  global.TestUtils.setupDOM();
  
  // Reset window globals
  delete window.Utils;
  delete window.ModelHandler;
  delete window.UIController;
  delete window.ErrorHandler;
  delete window.SeaweedClassifierApp;
});

// Cleanup after each test
afterEach(() => {
  // Clear any timers
  jest.clearAllTimers();
  
  // Clear DOM
  document.body.innerHTML = '';
});

// Global test timeout
jest.setTimeout(10000);
