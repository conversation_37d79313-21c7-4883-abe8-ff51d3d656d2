/* ==========================================================================
   Header Component Styles
   ========================================================================== */

.header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  max-width: 800px;
  position: relative;
}

.header__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: 0;
  padding: var(--spacing-xl) var(--spacing-2xl);
  border-radius: var(--radius-2xl);
  background: var(--gradient-card);
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-xl);
  line-height: var(--line-height-tight);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  position: relative;
  overflow: hidden;
}

.header__title::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.8s ease;
}

.header__title:hover::before {
  left: 100%;
}

.header__title:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-2xl);
  border-color: var(--color-primary-light);
}

.header__icon {
  font-size: 1.4em;
  display: inline-block;
  animation: float 4s ease-in-out infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  position: relative;
  z-index: 1;
}

.header__subtitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin: 0;
  opacity: 0.9;
}

/* ==========================================================================
   Header Animations
   ========================================================================== */

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* ==========================================================================
   Header Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .header__title {
    font-size: var(--font-size-2xl);
    padding: var(--spacing-md) var(--spacing-lg);
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .header__subtitle {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .header {
    margin-bottom: var(--spacing-xl);
  }
  
  .header__title {
    font-size: var(--font-size-xl);
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .header__subtitle {
    font-size: var(--font-size-sm);
  }
}
