/**
 * Application Configuration
 * Seaweed Health Classifier
 */

window.AppConfig = {
  // Model Configuration
  model: {
    url: "https://teachablemachine.withgoogle.com/models/SU3lmOLsW/",
    modelFile: "model.json",
    metadataFile: "metadata.json",
    maxRetries: 3,
    retryDelay: 1000, // milliseconds
    timeout: 30000 // 30 seconds
  },

  // UI Configuration
  ui: {
    maxImageSize: 10 * 1024 * 1024, // 10MB
    supportedFormats: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    imagePreview: {
      maxWidth: 320,
      maxHeight: 320,
      quality: 0.8
    },
    animations: {
      enabled: true,
      duration: 300,
      easing: 'ease-out'
    },
    toastDuration: 5000 // 5 seconds
  },

  // Prediction Configuration
  prediction: {
    confidenceThreshold: {
      high: 0.7,
      medium: 0.4,
      low: 0.0
    },
    maxPredictions: 5,
    sortByConfidence: true
  },

  // Error Messages
  messages: {
    errors: {
      modelLoad: "Failed to load the AI model. Please check your internet connection and try again.",
      modelTimeout: "Model loading timed out. Please refresh the page and try again.",
      imageLoad: "Failed to load the selected image. Please try a different image.",
      imageFormat: "Unsupported image format. Please use JPEG, PNG, or WebP images.",
      imageSize: "Image file is too large. Please use an image smaller than 10MB.",
      prediction: "Failed to analyze the image. Please try again.",
      network: "Network error. Please check your internet connection.",
      generic: "An unexpected error occurred. Please try again."
    },
    success: {
      modelLoaded: "AI model loaded successfully",
      imageLoaded: "Image loaded successfully",
      predictionComplete: "Analysis complete"
    },
    info: {
      modelLoading: "Loading AI model...",
      imageProcessing: "Processing image...",
      analyzing: "Analyzing seaweed health..."
    }
  },

  // Feature Flags
  features: {
    dragAndDrop: true,
    progressIndicator: true,
    detailedResults: true,
    exportResults: false,
    multipleImages: false,
    realTimeAnalysis: false
  },

  // Analytics (if needed)
  analytics: {
    enabled: false,
    trackingId: null,
    events: {
      modelLoad: 'model_loaded',
      imageUpload: 'image_uploaded',
      prediction: 'prediction_made',
      error: 'error_occurred'
    }
  },

  // Development Configuration
  development: {
    debug: false,
    verbose: false,
    mockPredictions: false,
    skipModelLoad: false
  },

  // Version Information
  version: "1.0.0",
  buildDate: new Date().toISOString(),
  
  // API Endpoints (for future use)
  api: {
    baseUrl: null,
    endpoints: {
      predict: '/api/predict',
      feedback: '/api/feedback',
      health: '/api/health'
    }
  }
};

// Environment-specific overrides
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // Development environment
  window.AppConfig.development.debug = true;
  window.AppConfig.development.verbose = true;
}

// Freeze configuration to prevent accidental modifications
Object.freeze(window.AppConfig);
