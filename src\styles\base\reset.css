/* ==========================================================================
   CSS Reset and Normalize
   ========================================================================== */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

/* Set core root defaults */
html:focus-within {
  scroll-behavior: smooth;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  line-height: var(--line-height-normal);
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
   scroll-behavior: auto;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ==========================================================================
   Additional Reset Styles
   ========================================================================== */

/* Remove button styling */
button {
  background: none;
  border: none;
  cursor: pointer;
}

/* Remove input styling */
input {
  border: none;
  outline: none;
}

/* Remove link styling */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove fieldset styling */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

/* Remove legend styling */
legend {
  padding: 0;
}

/* Remove table styling */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove hr styling */
hr {
  border: none;
  height: 1px;
  background: currentColor;
}

/* Remove details styling */
details {
  display: block;
}

/* Remove summary styling */
summary {
  display: list-item;
  cursor: pointer;
}

/* Remove template styling */
template {
  display: none;
}
