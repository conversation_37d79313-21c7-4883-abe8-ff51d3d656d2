/**
 * Utils Unit Tests
 */

// Load the utils module
require('../../src/scripts/utils.js');

describe('Utils', () => {
  beforeEach(() => {
    // Ensure Utils is available
    expect(window.Utils).toBeDefined();
  });

  describe('debounce', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('should delay function execution', () => {
      const mockFn = jest.fn();
      const debouncedFn = window.Utils.debounce(mockFn, 100);

      debouncedFn();
      expect(mockFn).not.toHaveBeenCalled();

      jest.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    test('should cancel previous calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = window.Utils.debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      jest.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    test('should execute immediately when immediate is true', () => {
      const mockFn = jest.fn();
      const debouncedFn = window.Utils.debounce(mockFn, 100, true);

      debouncedFn();
      expect(mockFn).toHaveBeenCalledTimes(1);

      debouncedFn();
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('throttle', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('should limit function execution frequency', () => {
      const mockFn = jest.fn();
      const throttledFn = window.Utils.throttle(mockFn, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(mockFn).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(100);
      throttledFn();
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('formatFileSize', () => {
    test('should format bytes correctly', () => {
      expect(window.Utils.formatFileSize(0)).toBe('0 Bytes');
      expect(window.Utils.formatFileSize(1024)).toBe('1 KB');
      expect(window.Utils.formatFileSize(1048576)).toBe('1 MB');
      expect(window.Utils.formatFileSize(1073741824)).toBe('1 GB');
    });

    test('should handle decimal values', () => {
      expect(window.Utils.formatFileSize(1536)).toBe('1.5 KB');
      expect(window.Utils.formatFileSize(2621440)).toBe('2.5 MB');
    });
  });

  describe('validateImageFile', () => {
    test('should validate supported image formats', () => {
      const jpegFile = TestUtils.createMockFile('test.jpg', 'image/jpeg');
      const pngFile = TestUtils.createMockFile('test.png', 'image/png');
      const webpFile = TestUtils.createMockFile('test.webp', 'image/webp');

      expect(window.Utils.validateImageFile(jpegFile).valid).toBe(true);
      expect(window.Utils.validateImageFile(pngFile).valid).toBe(true);
      expect(window.Utils.validateImageFile(webpFile).valid).toBe(true);
    });

    test('should reject unsupported formats', () => {
      const txtFile = TestUtils.createMockFile('test.txt', 'text/plain');
      const result = window.Utils.validateImageFile(txtFile);

      expect(result.valid).toBe(false);
      expect(result.error).toContain('format');
    });

    test('should reject files that are too large', () => {
      const largeFile = TestUtils.createMockFile('large.jpg', 'image/jpeg', 20 * 1024 * 1024);
      const result = window.Utils.validateImageFile(largeFile);

      expect(result.valid).toBe(false);
      expect(result.error).toContain('large');
    });

    test('should handle null file', () => {
      const result = window.Utils.validateImageFile(null);

      expect(result.valid).toBe(false);
      expect(result.error).toBe('No file selected');
    });
  });

  describe('createImageFromFile', () => {
    test('should create image from file', async () => {
      TestUtils.mockFileReader();
      const file = TestUtils.createMockFile('test.jpg', 'image/jpeg');

      const image = await window.Utils.createImageFromFile(file);

      expect(image).toBeInstanceOf(Image);
      expect(image.src).toBe('data:image/jpeg;base64,mock');
    });

    test('should reject on file read error', async () => {
      const mockReader = TestUtils.mockFileReader();
      mockReader.readAsDataURL = jest.fn(function() {
        setTimeout(() => {
          if (this.onerror) this.onerror(new Error('Read error'));
        }, 0);
      });

      const file = TestUtils.createMockFile('test.jpg', 'image/jpeg');

      await expect(window.Utils.createImageFromFile(file)).rejects.toThrow('Failed to read file');
    });
  });

  describe('formatPercentage', () => {
    test('should format percentage with default precision', () => {
      expect(window.Utils.formatPercentage(0.123)).toBe('12.3%');
      expect(window.Utils.formatPercentage(0.5)).toBe('50.0%');
      expect(window.Utils.formatPercentage(1)).toBe('100.0%');
    });

    test('should format percentage with custom precision', () => {
      expect(window.Utils.formatPercentage(0.12345, 2)).toBe('12.35%');
      expect(window.Utils.formatPercentage(0.12345, 0)).toBe('12%');
    });
  });

  describe('getConfidenceLevel', () => {
    test('should return correct confidence levels', () => {
      expect(window.Utils.getConfidenceLevel(0.8)).toBe('high');
      expect(window.Utils.getConfidenceLevel(0.5)).toBe('medium');
      expect(window.Utils.getConfidenceLevel(0.2)).toBe('low');
    });

    test('should handle edge cases', () => {
      expect(window.Utils.getConfidenceLevel(0.7)).toBe('high');
      expect(window.Utils.getConfidenceLevel(0.4)).toBe('medium');
      expect(window.Utils.getConfidenceLevel(0)).toBe('low');
    });
  });

  describe('generateId', () => {
    test('should generate unique IDs', () => {
      const id1 = window.Utils.generateId();
      const id2 = window.Utils.generateId();

      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
    });
  });

  describe('log', () => {
    test('should log messages in debug mode', () => {
      window.AppConfig.development.debug = true;

      window.Utils.log('info', 'Test message', { data: 'test' });

      expect(console.info).toHaveBeenCalled();
    });

    test('should not log messages when debug is disabled', () => {
      window.AppConfig.development.debug = false;

      window.Utils.log('info', 'Test message');

      expect(console.info).not.toHaveBeenCalled();
    });

    test('should use appropriate console methods', () => {
      window.AppConfig.development.debug = true;

      window.Utils.log('error', 'Error message');
      window.Utils.log('warn', 'Warning message');
      window.Utils.log('info', 'Info message');
      window.Utils.log('debug', 'Debug message');

      expect(console.error).toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalled();
      expect(console.info).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalled();
    });
  });

  describe('wait', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('should wait for specified time', async () => {
      const promise = window.Utils.wait(1000);
      
      jest.advanceTimersByTime(1000);
      
      await expect(promise).resolves.toBeUndefined();
    });
  });

  describe('retry', () => {
    test('should retry function on failure', async () => {
      let attempts = 0;
      const mockFn = jest.fn(() => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Test error');
        }
        return 'success';
      });

      const result = await window.Utils.retry(mockFn, 3, 10);

      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    test('should throw error after max retries', async () => {
      const mockFn = jest.fn(() => {
        throw new Error('Persistent error');
      });

      await expect(window.Utils.retry(mockFn, 2, 10)).rejects.toThrow('Persistent error');
      expect(mockFn).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
  });
});
