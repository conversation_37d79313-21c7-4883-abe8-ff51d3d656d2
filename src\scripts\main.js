/**
 * Main Application Entry Point
 * Seaweed Health Classifier
 */

(function() {
  'use strict';

  /**
   * Application class
   */
  class SeaweedClassifierApp {
    constructor() {
      this.initialized = false;
      this.version = window.AppConfig.version;
      this.startTime = Date.now();
    }

    /**
     * Initialize the application
     */
    async init() {
      if (this.initialized) {
        return;
      }

      try {
        window.Utils.log('info', `Initializing Seaweed Classifier v${this.version}`);
        
        // Check browser compatibility
        this.checkCompatibility();
        
        // Initialize error handling
        this.setupErrorHandling();
        
        // Initialize performance monitoring
        this.setupPerformanceMonitoring();
        
        // Wait for DOM to be ready
        await this.waitForDOM();
        
        // Initialize components
        await this.initializeComponents();
        
        // Setup analytics (if enabled)
        this.setupAnalytics();
        
        // Mark as initialized
        this.initialized = true;
        
        const initTime = Date.now() - this.startTime;
        window.Utils.log('info', `Application initialized successfully in ${initTime}ms`);
        
        // Dispatch ready event
        this.dispatchReadyEvent();
        
      } catch (error) {
        window.Utils.log('error', 'Application initialization failed', error);
        this.handleInitializationError(error);
      }
    }

    /**
     * Check browser compatibility
     */
    checkCompatibility() {
      const requiredFeatures = [
        'Promise',
        'fetch',
        'FileReader',
        'addEventListener'
      ];

      const missingFeatures = requiredFeatures.filter(feature => 
        typeof window[feature] === 'undefined'
      );

      if (missingFeatures.length > 0) {
        throw new Error(`Browser missing required features: ${missingFeatures.join(', ')}`);
      }

      // Check for WebGL support (required for TensorFlow.js)
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) {
        window.Utils.log('warn', 'WebGL not supported, model performance may be reduced');
      }

      window.Utils.log('info', 'Browser compatibility check passed');
    }

    /**
     * Setup global error handling
     */
    setupErrorHandling() {
      // Handle unhandled promise rejections
      window.addEventListener('unhandledrejection', (event) => {
        window.Utils.log('error', 'Unhandled promise rejection', event.reason);
        
        // Show user-friendly error message
        window.Utils.showToast(
          'An unexpected error occurred. Please try refreshing the page.',
          'error'
        );
        
        // Prevent default browser error handling
        event.preventDefault();
      });

      // Handle JavaScript errors
      window.addEventListener('error', (event) => {
        window.Utils.log('error', 'JavaScript error', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        });
      });

      window.Utils.log('info', 'Error handling setup complete');
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
      if (!window.AppConfig.development.debug) {
        return;
      }

      // Monitor page load performance
      window.addEventListener('load', () => {
        if (performance.timing) {
          const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
          window.Utils.log('info', `Page load time: ${loadTime}ms`);
        }
      });

      // Monitor memory usage (if available)
      if (performance.memory) {
        setInterval(() => {
          const memory = performance.memory;
          window.Utils.log('debug', 'Memory usage', {
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB',
            limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
          });
        }, 30000); // Every 30 seconds
      }
    }

    /**
     * Wait for DOM to be ready
     */
    waitForDOM() {
      return new Promise((resolve) => {
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', resolve);
        } else {
          resolve();
        }
      });
    }

    /**
     * Initialize application components
     */
    async initializeComponents() {
      window.Utils.log('info', 'Initializing components');
      
      // Components are already initialized via their respective files
      // This method can be used for additional setup if needed
      
      // Verify all components are available
      const requiredComponents = ['Utils', 'ModelHandler', 'UIController'];
      const missingComponents = requiredComponents.filter(component => 
        typeof window[component] === 'undefined'
      );

      if (missingComponents.length > 0) {
        throw new Error(`Missing required components: ${missingComponents.join(', ')}`);
      }

      window.Utils.log('info', 'All components initialized successfully');
    }

    /**
     * Setup analytics (if enabled)
     */
    setupAnalytics() {
      const analytics = window.AppConfig.analytics;
      
      if (!analytics.enabled || !analytics.trackingId) {
        return;
      }

      // Initialize analytics here if needed
      window.Utils.log('info', 'Analytics setup complete');
    }

    /**
     * Handle initialization errors
     */
    handleInitializationError(error) {
      // Show critical error message
      const errorHTML = `
        <div style="
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: #f44336;
          color: white;
          padding: 20px;
          border-radius: 8px;
          text-align: center;
          z-index: 9999;
          max-width: 400px;
        ">
          <h3>Application Failed to Load</h3>
          <p>${error.message}</p>
          <button onclick="location.reload()" style="
            background: white;
            color: #f44336;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
          ">
            Reload Page
          </button>
        </div>
      `;
      
      document.body.insertAdjacentHTML('beforeend', errorHTML);
    }

    /**
     * Dispatch application ready event
     */
    dispatchReadyEvent() {
      const event = new CustomEvent('seaweedClassifierReady', {
        detail: {
          version: this.version,
          initTime: Date.now() - this.startTime
        }
      });
      
      window.dispatchEvent(event);
    }

    /**
     * Get application status
     */
    getStatus() {
      return {
        initialized: this.initialized,
        version: this.version,
        modelReady: window.ModelHandler ? window.ModelHandler.isReady() : false,
        uptime: Date.now() - this.startTime
      };
    }

    /**
     * Cleanup application resources
     */
    cleanup() {
      window.Utils.log('info', 'Cleaning up application resources');
      
      if (window.ModelHandler) {
        window.ModelHandler.cleanup();
      }
      
      this.initialized = false;
    }
  }

  // Create and initialize application instance
  const app = new SeaweedClassifierApp();
  
  // Make app globally available for debugging
  window.SeaweedClassifierApp = app;
  
  // Initialize when script loads
  app.init();

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    app.cleanup();
  });

  // Export for potential module use
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = SeaweedClassifierApp;
  }

})();
