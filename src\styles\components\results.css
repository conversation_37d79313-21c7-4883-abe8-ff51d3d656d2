/* ==========================================================================
   Results Component Styles
   ========================================================================== */

.results-container {
  background: var(--color-bg-secondary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-primary);
  opacity: 0;
  transform: translateY(20px);
}

.results-container.visible {
  opacity: 1;
  transform: translateY(0);
}

.results-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.predictions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.prediction-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(4px);
  box-shadow: var(--shadow-inset);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.prediction-item:hover {
  transform: translateX(4px);
  background: rgba(0, 0, 0, 0.35);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.prediction-class {
  font-weight: var(--font-weight-semibold);
}

.prediction-percentage {
  font-weight: var(--font-weight-bold);
  color: var(--color-success);
}

.prediction-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.prediction-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success), var(--color-primary));
  border-radius: var(--radius-sm);
  transition: width 0.8s ease-out;
  position: relative;
}

.prediction-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Prediction confidence levels */
.prediction-item.high-confidence .prediction-percentage {
  color: var(--color-success);
}

.prediction-item.medium-confidence .prediction-percentage {
  color: var(--color-warning);
}

.prediction-item.low-confidence .prediction-percentage {
  color: var(--color-error);
}

.prediction-item.high-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-success), #00cc99);
}

.prediction-item.medium-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-warning), #ffb74d);
}

.prediction-item.low-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-error), #ef5350);
}

/* Results actions */
.results-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* Empty state */
.results-empty {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-muted);
}

.results-empty::before {
  content: '🔍';
  font-size: var(--font-size-3xl);
  display: block;
  margin-bottom: var(--spacing-md);
}

/* ==========================================================================
   Results Animations
   ========================================================================== */

@keyframes resultSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.prediction-item {
  animation: resultSlideIn 0.5s ease-out;
}

.prediction-item:nth-child(1) { animation-delay: 0.1s; }
.prediction-item:nth-child(2) { animation-delay: 0.2s; }
.prediction-item:nth-child(3) { animation-delay: 0.3s; }
.prediction-item:nth-child(4) { animation-delay: 0.4s; }
.prediction-item:nth-child(5) { animation-delay: 0.5s; }

/* ==========================================================================
   Results Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .results-container {
    padding: var(--spacing-lg);
  }
  
  .prediction-header {
    font-size: var(--font-size-base);
  }
  
  .results-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .results-container {
    padding: var(--spacing-md);
  }
  
  .results-title {
    font-size: var(--font-size-lg);
  }
  
  .prediction-item {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
