/* ==========================================================================
   Results Component Styles
   ========================================================================== */

.results-container {
  background: var(--gradient-card);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(16px);
  box-shadow: var(--shadow-2xl);
  transition: all var(--transition-slow);
  border: 2px solid var(--color-border-secondary);
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  position: relative;
  overflow: hidden;
}

.results-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0.5;
  pointer-events: none;
}

.results-container.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
  animation: resultsAppear 0.8s ease-out;
}

.results-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.predictions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.prediction-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--color-bg-glass);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-primary);
  margin-bottom: var(--spacing-sm);
}

.prediction-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--gradient-success);
  transition: width var(--transition-normal);
}

.prediction-item:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary-light);
}

.prediction-item:hover::before {
  width: 8px;
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.prediction-class {
  font-weight: var(--font-weight-semibold);
}

.prediction-percentage {
  font-weight: var(--font-weight-bold);
  color: var(--color-success);
}

.prediction-bar {
  height: 10px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.prediction-fill {
  height: 100%;
  background: var(--gradient-success);
  border-radius: var(--radius-full);
  transition: width 1.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 230, 118, 0.3);
}

.prediction-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Prediction confidence levels */
.prediction-item.high-confidence .prediction-percentage {
  color: var(--color-success);
}

.prediction-item.medium-confidence .prediction-percentage {
  color: var(--color-warning);
}

.prediction-item.low-confidence .prediction-percentage {
  color: var(--color-error);
}

.prediction-item.high-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-success), #00cc99);
}

.prediction-item.medium-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-warning), #ffb74d);
}

.prediction-item.low-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-error), #ef5350);
}

/* Results actions */
.results-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* Empty state */
.results-empty {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-muted);
}

.results-empty::before {
  content: '🔍';
  font-size: var(--font-size-3xl);
  display: block;
  margin-bottom: var(--spacing-md);
}

/* ==========================================================================
   Enhanced Results Animations
   ========================================================================== */

@keyframes resultsAppear {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.9);
  }
  50% {
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes resultSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
  }
  50% {
    transform: translateX(5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes progressFill {
  0% {
    width: 0%;
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.prediction-item {
  animation: resultSlideIn 0.5s ease-out;
}

.prediction-item:nth-child(1) { animation-delay: 0.1s; }
.prediction-item:nth-child(2) { animation-delay: 0.2s; }
.prediction-item:nth-child(3) { animation-delay: 0.3s; }
.prediction-item:nth-child(4) { animation-delay: 0.4s; }
.prediction-item:nth-child(5) { animation-delay: 0.5s; }

/* ==========================================================================
   Results Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .results-container {
    padding: var(--spacing-lg);
  }
  
  .prediction-header {
    font-size: var(--font-size-base);
  }
  
  .results-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .results-container {
    padding: var(--spacing-md);
  }
  
  .results-title {
    font-size: var(--font-size-lg);
  }
  
  .prediction-item {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
