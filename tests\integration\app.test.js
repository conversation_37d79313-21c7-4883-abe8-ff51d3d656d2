/**
 * Application Integration Tests
 */

// Load all application modules
require('../../src/scripts/utils.js');
require('../../src/scripts/error-handler.js');
require('../../src/scripts/model-handler.js');
require('../../src/scripts/ui-controller.js');
require('../../src/scripts/main.js');

describe('Application Integration', () => {
  beforeEach(() => {
    // Reset all modules
    if (window.ModelHandler) {
      window.ModelHandler.cleanup();
    }
    
    // Setup DOM
    TestUtils.setupDOM();
  });

  describe('Full workflow', () => {
    test('should complete image upload and prediction workflow', async () => {
      // Mock successful model loading
      const mockModel = {
        predict: jest.fn().mockResolvedValue([
          { className: 'Healthy', probability: 0.8 },
          { className: 'Diseased', probability: 0.2 }
        ]),
        dispose: jest.fn()
      };
      global.tmImage.load.mockResolvedValue(mockModel);

      // Initialize application components
      window.ErrorHandler.init();
      window.UIController.init();
      await window.ModelHandler.init();

      // Create mock file and image
      TestUtils.mockFileReader();
      const file = TestUtils.createMockFile('test.jpg', 'image/jpeg');

      // Simulate file upload
      const fileInput = document.getElementById('fileInput');
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false
      });

      // Trigger file change event
      const changeEvent = new Event('change');
      fileInput.dispatchEvent(changeEvent);

      // Wait for file processing
      await TestUtils.nextTick();

      // Verify image preview is shown
      const imagePreview = document.getElementById('image-preview');
      expect(imagePreview.style.display).toBe('block');
      expect(imagePreview.src).toBe('data:image/jpeg;base64,mock');

      // Verify predict button is enabled
      const predictButton = document.getElementById('predict-button');
      expect(predictButton.disabled).toBe(false);

      // Simulate prediction
      predictButton.click();

      // Wait for prediction to complete
      await TestUtils.nextTick();

      // Verify results are displayed
      const resultsContainer = document.getElementById('results-container');
      expect(resultsContainer.style.display).toBe('block');

      const predictionsList = document.getElementById('predictions-list');
      expect(predictionsList.children.length).toBe(2);

      // Verify prediction items
      const firstPrediction = predictionsList.children[0];
      expect(firstPrediction.textContent).toContain('Healthy');
      expect(firstPrediction.textContent).toContain('80.0%');
    });

    test('should handle file validation errors', async () => {
      window.ErrorHandler.init();
      window.UIController.init();

      // Create invalid file
      const invalidFile = TestUtils.createMockFile('test.txt', 'text/plain');

      // Simulate file upload
      const fileInput = document.getElementById('fileInput');
      Object.defineProperty(fileInput, 'files', {
        value: [invalidFile],
        writable: false
      });

      const changeEvent = new Event('change');
      fileInput.dispatchEvent(changeEvent);

      await TestUtils.nextTick();

      // Verify error is shown
      const errorContainer = document.getElementById('error-container');
      expect(errorContainer.style.display).toBe('flex');

      const errorMessage = document.getElementById('error-message');
      expect(errorMessage.textContent).toContain('format');
    });

    test('should handle model loading errors', async () => {
      // Mock model loading failure
      global.tmImage.load.mockRejectedValue(new Error('Network error'));

      window.ErrorHandler.init();
      window.UIController.init();

      try {
        await window.ModelHandler.init();
      } catch (error) {
        // Expected to fail
      }

      // Verify error handling
      expect(window.ModelHandler.isLoaded).toBe(false);
      
      // Verify error is displayed
      const errorContainer = document.getElementById('error-container');
      expect(errorContainer.style.display).toBe('flex');
    });

    test('should handle prediction errors', async () => {
      // Mock successful model loading but failed prediction
      const mockModel = {
        predict: jest.fn().mockRejectedValue(new Error('Prediction failed')),
        dispose: jest.fn()
      };
      global.tmImage.load.mockResolvedValue(mockModel);

      window.ErrorHandler.init();
      window.UIController.init();
      await window.ModelHandler.init();

      // Setup file and image
      TestUtils.mockFileReader();
      const file = TestUtils.createMockFile('test.jpg', 'image/jpeg');

      // Upload file
      const fileInput = document.getElementById('fileInput');
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false
      });

      const changeEvent = new Event('change');
      fileInput.dispatchEvent(changeEvent);

      await TestUtils.nextTick();

      // Attempt prediction
      const predictButton = document.getElementById('predict-button');
      predictButton.click();

      await TestUtils.nextTick();

      // Verify error handling
      const errorContainer = document.getElementById('error-container');
      expect(errorContainer.style.display).toBe('flex');

      const errorMessage = document.getElementById('error-message');
      expect(errorMessage.textContent).toContain('analyze');
    });

    test('should reset application state', async () => {
      // Setup application with loaded image and results
      const mockModel = {
        predict: jest.fn().mockResolvedValue([
          { className: 'Healthy', probability: 0.8 }
        ]),
        dispose: jest.fn()
      };
      global.tmImage.load.mockResolvedValue(mockModel);

      window.ErrorHandler.init();
      window.UIController.init();
      await window.ModelHandler.init();

      // Upload and predict
      TestUtils.mockFileReader();
      const file = TestUtils.createMockFile('test.jpg', 'image/jpeg');

      const fileInput = document.getElementById('fileInput');
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false
      });

      fileInput.dispatchEvent(new Event('change'));
      await TestUtils.nextTick();

      const predictButton = document.getElementById('predict-button');
      predictButton.click();
      await TestUtils.nextTick();

      // Verify state before reset
      const imagePreview = document.getElementById('image-preview');
      const resultsContainer = document.getElementById('results-container');
      expect(imagePreview.style.display).toBe('block');
      expect(resultsContainer.style.display).toBe('block');

      // Reset application
      const resetButton = document.getElementById('reset-button');
      resetButton.click();

      // Verify state after reset
      expect(imagePreview.style.display).toBe('none');
      expect(resultsContainer.style.display).toBe('none');
      expect(fileInput.value).toBe('');
    });
  });

  describe('Error recovery', () => {
    test('should retry model loading on failure', async () => {
      const mockModel = {
        predict: jest.fn().mockResolvedValue([]),
        dispose: jest.fn()
      };

      // Fail first two attempts, succeed on third
      global.tmImage.load
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue(mockModel);

      window.ErrorHandler.init();
      await window.ModelHandler.init();

      expect(global.tmImage.load).toHaveBeenCalledTimes(3);
      expect(window.ModelHandler.isLoaded).toBe(true);
    });

    test('should handle retry button click', async () => {
      // Mock initial failure
      global.tmImage.load.mockRejectedValue(new Error('Initial failure'));

      window.ErrorHandler.init();
      window.UIController.init();

      try {
        await window.ModelHandler.init();
      } catch (error) {
        // Expected to fail
      }

      // Mock successful retry
      const mockModel = {
        predict: jest.fn().mockResolvedValue([]),
        dispose: jest.fn()
      };
      global.tmImage.load.mockResolvedValue(mockModel);

      // Click retry button
      const retryButton = document.getElementById('retry-button');
      retryButton.click();

      await TestUtils.nextTick();

      // Verify model is loaded after retry
      expect(window.ModelHandler.isLoaded).toBe(true);
    });
  });

  describe('Accessibility', () => {
    test('should support keyboard navigation', async () => {
      window.UIController.init();

      const predictButton = document.getElementById('predict-button');
      
      // Simulate Enter key press
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      Object.defineProperty(enterEvent, 'target', { value: predictButton });
      
      document.dispatchEvent(enterEvent);

      // Should not crash (button is disabled without image)
      expect(predictButton.disabled).toBe(true);
    });

    test('should support Escape key for reset', async () => {
      window.UIController.init();

      // Setup some state
      const imagePreview = document.getElementById('image-preview');
      imagePreview.style.display = 'block';

      // Simulate Escape key press
      const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
      document.dispatchEvent(escapeEvent);

      // Should reset the application
      expect(imagePreview.style.display).toBe('none');
    });
  });

  describe('Performance', () => {
    test('should handle large images efficiently', async () => {
      const mockModel = {
        predict: jest.fn().mockResolvedValue([
          { className: 'Healthy', probability: 0.8 }
        ]),
        dispose: jest.fn()
      };
      global.tmImage.load.mockResolvedValue(mockModel);

      window.UIController.init();
      await window.ModelHandler.init();

      // Create large image file
      const largeFile = TestUtils.createMockFile(
        'large.jpg', 
        'image/jpeg', 
        5 * 1024 * 1024 // 5MB
      );

      TestUtils.mockFileReader();

      const startTime = Date.now();

      // Process large file
      const fileInput = document.getElementById('fileInput');
      Object.defineProperty(fileInput, 'files', {
        value: [largeFile],
        writable: false
      });

      fileInput.dispatchEvent(new Event('change'));
      await TestUtils.nextTick();

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should process within reasonable time (less than 1 second for mock)
      expect(processingTime).toBeLessThan(1000);
    });
  });
});
