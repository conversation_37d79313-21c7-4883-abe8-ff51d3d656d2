/**
 * Environment Configuration
 * Seaweed Health Classifier
 */

(function() {
  'use strict';

  /**
   * Detect current environment
   */
  function detectEnvironment() {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;
    const port = window.location.port;

    // Development environment
    if (hostname === 'localhost' || 
        hostname === '127.0.0.1' || 
        hostname.startsWith('192.168.') ||
        hostname.endsWith('.local') ||
        port === '3000' || 
        port === '8080' || 
        port === '5000') {
      return 'development';
    }

    // Staging environment
    if (hostname.includes('staging') || 
        hostname.includes('test') || 
        hostname.includes('dev')) {
      return 'staging';
    }

    // Production environment
    return 'production';
  }

  /**
   * Environment-specific configurations
   */
  const environments = {
    development: {
      debug: true,
      verbose: true,
      mockPredictions: false,
      skipModelLoad: false,
      analytics: {
        enabled: false,
        trackingId: null
      },
      api: {
        baseUrl: 'http://localhost:3001',
        timeout: 10000
      },
      features: {
        dragAndDrop: true,
        progressIndicator: true,
        detailedResults: true,
        exportResults: true,
        multipleImages: false,
        realTimeAnalysis: false
      },
      ui: {
        toastDuration: 3000,
        animations: {
          enabled: true,
          duration: 300
        }
      }
    },

    staging: {
      debug: true,
      verbose: false,
      mockPredictions: false,
      skipModelLoad: false,
      analytics: {
        enabled: true,
        trackingId: 'GA_STAGING_ID'
      },
      api: {
        baseUrl: 'https://staging-api.seaweedclassifier.com',
        timeout: 15000
      },
      features: {
        dragAndDrop: true,
        progressIndicator: true,
        detailedResults: true,
        exportResults: false,
        multipleImages: false,
        realTimeAnalysis: false
      },
      ui: {
        toastDuration: 4000,
        animations: {
          enabled: true,
          duration: 300
        }
      }
    },

    production: {
      debug: false,
      verbose: false,
      mockPredictions: false,
      skipModelLoad: false,
      analytics: {
        enabled: true,
        trackingId: 'GA_PRODUCTION_ID'
      },
      api: {
        baseUrl: 'https://api.seaweedclassifier.com',
        timeout: 20000
      },
      features: {
        dragAndDrop: true,
        progressIndicator: true,
        detailedResults: true,
        exportResults: false,
        multipleImages: false,
        realTimeAnalysis: false
      },
      ui: {
        toastDuration: 5000,
        animations: {
          enabled: true,
          duration: 300
        }
      }
    }
  };

  /**
   * Get current environment configuration
   */
  function getCurrentConfig() {
    const env = detectEnvironment();
    const config = environments[env] || environments.production;
    
    return {
      environment: env,
      ...config
    };
  }

  /**
   * Apply environment configuration to AppConfig
   */
  function applyEnvironmentConfig() {
    if (typeof window.AppConfig === 'undefined') {
      console.warn('AppConfig not found, environment configuration not applied');
      return;
    }

    const envConfig = getCurrentConfig();
    
    // Deep merge environment config with AppConfig
    window.AppConfig.environment = envConfig.environment;
    
    // Override development settings
    Object.assign(window.AppConfig.development, {
      debug: envConfig.debug,
      verbose: envConfig.verbose,
      mockPredictions: envConfig.mockPredictions,
      skipModelLoad: envConfig.skipModelLoad
    });

    // Override analytics settings
    Object.assign(window.AppConfig.analytics, envConfig.analytics);

    // Override API settings
    if (envConfig.api) {
      Object.assign(window.AppConfig.api, envConfig.api);
    }

    // Override feature flags
    Object.assign(window.AppConfig.features, envConfig.features);

    // Override UI settings
    if (envConfig.ui) {
      if (envConfig.ui.toastDuration) {
        window.AppConfig.ui.toastDuration = envConfig.ui.toastDuration;
      }
      if (envConfig.ui.animations) {
        Object.assign(window.AppConfig.ui.animations, envConfig.ui.animations);
      }
    }

    console.log(`Environment: ${envConfig.environment}`, envConfig);
  }

  /**
   * Initialize environment configuration
   */
  function init() {
    // Apply configuration when AppConfig is available
    if (typeof window.AppConfig !== 'undefined') {
      applyEnvironmentConfig();
    } else {
      // Wait for AppConfig to be loaded
      const checkAppConfig = setInterval(() => {
        if (typeof window.AppConfig !== 'undefined') {
          clearInterval(checkAppConfig);
          applyEnvironmentConfig();
        }
      }, 100);
      
      // Timeout after 5 seconds
      setTimeout(() => {
        clearInterval(checkAppConfig);
        console.warn('AppConfig not loaded within timeout, using default configuration');
      }, 5000);
    }
  }

  // Export environment utilities
  window.Environment = {
    detect: detectEnvironment,
    getConfig: getCurrentConfig,
    apply: applyEnvironmentConfig,
    init: init
  };

  // Auto-initialize
  init();

})();
