# API Documentation

## Overview

The Seaweed Health Classifier provides a JavaScript API for programmatic interaction with the application components.

## Global Objects

### AppConfig

Application configuration object containing all settings and constants.

```javascript
window.AppConfig = {
  model: { /* model settings */ },
  ui: { /* UI settings */ },
  prediction: { /* prediction settings */ },
  messages: { /* error/success messages */ },
  features: { /* feature flags */ }
}
```

### Utils

Utility functions for common operations.

#### Methods

##### `debounce(func, wait, immediate)`
Debounce function calls to improve performance.

**Parameters:**
- `func` (Function): Function to debounce
- `wait` (number): Wait time in milliseconds
- `immediate` (boolean): Execute immediately

**Returns:** Function - Debounced function

**Example:**
```javascript
const debouncedSearch = Utils.debounce(searchFunction, 300);
```

##### `validateImageFile(file)`
Validate uploaded image file.

**Parameters:**
- `file` (File): File object to validate

**Returns:** Object - Validation result
```javascript
{
  valid: boolean,
  error?: string
}
```

##### `formatPercentage(value, precision)`
Format decimal value as percentage.

**Parameters:**
- `value` (number): Decimal value (0-1)
- `precision` (number): Decimal places (default: 1)

**Returns:** string - Formatted percentage

##### `showToast(message, type, duration)`
Display toast notification.

**Parameters:**
- `message` (string): Toast message
- `type` (string): Toast type ('success', 'error', 'warning', 'info')
- `duration` (number): Display duration in milliseconds

**Returns:** HTMLElement - Toast element

### ModelHandler

Manages TensorFlow.js model loading and predictions.

#### Properties

- `model` (Object): Loaded TensorFlow.js model
- `isLoading` (boolean): Model loading state
- `isLoaded` (boolean): Model loaded state

#### Methods

##### `init()`
Initialize and load the model.

**Returns:** Promise<void>

**Example:**
```javascript
await ModelHandler.init();
```

##### `predict(imageElement)`
Make prediction on image.

**Parameters:**
- `imageElement` (HTMLImageElement): Image to analyze

**Returns:** Promise<Array> - Prediction results
```javascript
[
  {
    className: string,
    probability: number,
    confidence: string,
    percentage: string
  }
]
```

##### `isReady()`
Check if model is ready for predictions.

**Returns:** boolean

##### `cleanup()`
Cleanup model resources.

### UIController

Manages user interface interactions and state.

#### Properties

- `elements` (Object): Cached DOM elements
- `state` (Object): Current UI state

#### Methods

##### `init()`
Initialize UI controller.

##### `showLoading(message)`
Show loading indicator.

**Parameters:**
- `message` (string): Loading message

##### `hideLoading()`
Hide loading indicator.

##### `showError(message, type)`
Show error message.

**Parameters:**
- `message` (string): Error message
- `type` (string): Error type

##### `clearAll()`
Clear all UI state and reset interface.

## Events

### Custom Events

The application dispatches custom events for integration:

#### `seaweedClassifierReady`
Fired when application is fully initialized.

```javascript
window.addEventListener('seaweedClassifierReady', (event) => {
  console.log('App ready:', event.detail);
});
```

**Event Detail:**
```javascript
{
  version: string,
  initTime: number
}
```

## Error Handling

### Error Types

- `ModelError`: Model loading or prediction errors
- `FileError`: File validation or processing errors
- `NetworkError`: Network connectivity issues

### Error Messages

All error messages are defined in `AppConfig.messages.errors`:

```javascript
{
  modelLoad: "Failed to load the AI model...",
  imageLoad: "Failed to load the selected image...",
  prediction: "Failed to analyze the image...",
  // ... more messages
}
```

## Configuration

### Model Configuration

```javascript
AppConfig.model = {
  url: "https://teachablemachine.withgoogle.com/models/MODEL_ID/",
  modelFile: "model.json",
  metadataFile: "metadata.json",
  maxRetries: 3,
  retryDelay: 1000,
  timeout: 30000
}
```

### UI Configuration

```javascript
AppConfig.ui = {
  maxImageSize: 10 * 1024 * 1024, // 10MB
  supportedFormats: ['image/jpeg', 'image/png', 'image/webp'],
  imagePreview: {
    maxWidth: 320,
    maxHeight: 320
  },
  toastDuration: 5000
}
```

### Feature Flags

```javascript
AppConfig.features = {
  dragAndDrop: true,
  progressIndicator: true,
  detailedResults: true,
  exportResults: false
}
```

## Integration Examples

### Basic Usage

```javascript
// Wait for app to be ready
window.addEventListener('seaweedClassifierReady', async () => {
  // Upload and analyze image programmatically
  const fileInput = document.getElementById('fileInput');
  const file = fileInput.files[0];
  
  if (file) {
    const validation = Utils.validateImageFile(file);
    if (validation.valid) {
      const imageElement = await Utils.createImageFromFile(file);
      const predictions = await ModelHandler.predict(imageElement);
      console.log('Predictions:', predictions);
    }
  }
});
```

### Custom Error Handling

```javascript
// Override default error handling
const originalShowError = UIController.showError;
UIController.showError = function(message, type) {
  // Custom error handling logic
  console.error('Custom error handler:', message, type);
  
  // Call original method
  originalShowError.call(this, message, type);
};
```

### Configuration Override

```javascript
// Override configuration before app initialization
window.AppConfig.ui.toastDuration = 3000;
window.AppConfig.features.dragAndDrop = false;
```

## Browser Compatibility

### Minimum Requirements

- ES6+ support
- Promise support
- Fetch API
- FileReader API
- WebGL (for optimal performance)

### Polyfills

For older browsers, include these polyfills:

```html
<script src="https://polyfill.io/v3/polyfill.min.js?features=es6,fetch,promise"></script>
```

## Performance Considerations

### Model Loading

- Model is loaded asynchronously on page load
- Subsequent predictions are fast (cached model)
- Consider preloading for better UX

### Memory Management

- Model resources are automatically cleaned up
- Large images are resized before processing
- Toast notifications are automatically removed

### Network Optimization

- Model files are cached by browser
- Retry logic handles network failures
- Timeout prevents hanging requests
