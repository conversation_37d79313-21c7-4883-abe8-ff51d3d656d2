/**
 * ModelHandler Unit Tests
 */

// Load the model handler module
require('../../src/scripts/model-handler.js');

describe('ModelHandler', () => {
  beforeEach(() => {
    // Ensure ModelHandler is available
    expect(window.ModelHandler).toBeDefined();
    
    // Reset model state
    window.ModelHandler.model = null;
    window.ModelHandler.isLoading = false;
    window.ModelHandler.isLoaded = false;
  });

  describe('init', () => {
    test('should initialize model successfully', async () => {
      const mockModel = {
        predict: jest.fn().mockResolvedValue([
          { className: 'Healthy', probability: 0.8 },
          { className: 'Diseased', probability: 0.2 }
        ])
      };
      
      global.tmImage.load.mockResolvedValue(mockModel);

      await window.ModelHandler.init();

      expect(window.ModelHandler.isLoaded).toBe(true);
      expect(window.ModelHandler.model).toBe(mockModel);
      expect(global.tmImage.load).toHaveBeenCalledWith(
        expect.stringContaining('model.json'),
        expect.stringContaining('metadata.json')
      );
    });

    test('should not reinitialize if already loaded', async () => {
      window.ModelHandler.isLoaded = true;
      window.ModelHandler.model = { mock: 'model' };

      await window.ModelHandler.init();

      expect(global.tmImage.load).not.toHaveBeenCalled();
    });

    test('should not reinitialize if already loading', async () => {
      window.ModelHandler.isLoading = true;

      await window.ModelHandler.init();

      expect(global.tmImage.load).not.toHaveBeenCalled();
    });

    test('should handle model loading failure', async () => {
      global.tmImage.load.mockRejectedValue(new Error('Network error'));

      await expect(window.ModelHandler.init()).rejects.toThrow();
      expect(window.ModelHandler.isLoaded).toBe(false);
      expect(window.ModelHandler.isLoading).toBe(false);
    });

    test('should retry on failure', async () => {
      global.tmImage.load
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue({ predict: jest.fn() });

      await window.ModelHandler.init();

      expect(global.tmImage.load).toHaveBeenCalledTimes(3);
      expect(window.ModelHandler.isLoaded).toBe(true);
    });
  });

  describe('predict', () => {
    beforeEach(() => {
      window.ModelHandler.isLoaded = true;
      window.ModelHandler.model = {
        predict: jest.fn().mockResolvedValue([
          { className: 'Healthy', probability: 0.85 },
          { className: 'Diseased', probability: 0.15 }
        ])
      };
    });

    test('should make prediction successfully', async () => {
      const mockImage = TestUtils.createMockImage();

      const predictions = await window.ModelHandler.predict(mockImage);

      expect(predictions).toHaveLength(2);
      expect(predictions[0]).toMatchObject({
        className: 'Healthy',
        probability: 0.85,
        confidence: 'high',
        percentage: '85.0%'
      });
      expect(predictions[1]).toMatchObject({
        className: 'Diseased',
        probability: 0.15,
        confidence: 'low',
        percentage: '15.0%'
      });
    });

    test('should sort predictions by confidence', async () => {
      window.ModelHandler.model.predict.mockResolvedValue([
        { className: 'Low', probability: 0.2 },
        { className: 'High', probability: 0.8 },
        { className: 'Medium', probability: 0.5 }
      ]);

      const mockImage = TestUtils.createMockImage();
      const predictions = await window.ModelHandler.predict(mockImage);

      expect(predictions[0].className).toBe('High');
      expect(predictions[1].className).toBe('Medium');
      expect(predictions[2].className).toBe('Low');
    });

    test('should limit number of predictions', async () => {
      const originalMaxPredictions = window.AppConfig.prediction.maxPredictions;
      window.AppConfig.prediction.maxPredictions = 2;

      window.ModelHandler.model.predict.mockResolvedValue([
        { className: 'First', probability: 0.4 },
        { className: 'Second', probability: 0.3 },
        { className: 'Third', probability: 0.2 },
        { className: 'Fourth', probability: 0.1 }
      ]);

      const mockImage = TestUtils.createMockImage();
      const predictions = await window.ModelHandler.predict(mockImage);

      expect(predictions).toHaveLength(2);
      
      // Restore original value
      window.AppConfig.prediction.maxPredictions = originalMaxPredictions;
    });

    test('should throw error if model not loaded', async () => {
      window.ModelHandler.isLoaded = false;
      window.ModelHandler.model = null;

      const mockImage = TestUtils.createMockImage();

      await expect(window.ModelHandler.predict(mockImage)).rejects.toThrow('Model not loaded');
    });

    test('should wait for image to load', async () => {
      const mockImage = TestUtils.createMockImage();
      mockImage.complete = false;

      // Simulate image loading
      setTimeout(() => {
        mockImage.complete = true;
        if (mockImage.onload) mockImage.onload();
      }, 100);

      const predictions = await window.ModelHandler.predict(mockImage);

      expect(predictions).toBeDefined();
      expect(window.ModelHandler.model.predict).toHaveBeenCalledWith(mockImage);
    });

    test('should handle image load timeout', async () => {
      const mockImage = TestUtils.createMockImage();
      mockImage.complete = false;
      
      // Don't trigger onload to simulate timeout

      await expect(window.ModelHandler.predict(mockImage)).rejects.toThrow('Image load timeout');
    });
  });

  describe('isReady', () => {
    test('should return true when model is loaded', () => {
      window.ModelHandler.isLoaded = true;
      window.ModelHandler.model = { predict: jest.fn() };

      expect(window.ModelHandler.isReady()).toBe(true);
    });

    test('should return false when model is not loaded', () => {
      window.ModelHandler.isLoaded = false;
      window.ModelHandler.model = null;

      expect(window.ModelHandler.isReady()).toBe(false);
    });

    test('should return false when model is null', () => {
      window.ModelHandler.isLoaded = true;
      window.ModelHandler.model = null;

      expect(window.ModelHandler.isReady()).toBe(false);
    });
  });

  describe('getModelInfo', () => {
    test('should return model info when loaded', () => {
      window.ModelHandler.isLoaded = true;
      window.ModelHandler.model = { predict: jest.fn() };

      const info = window.ModelHandler.getModelInfo();

      expect(info).toMatchObject({
        loaded: true,
        modelUrl: expect.stringContaining('teachablemachine'),
        version: expect.any(String)
      });
    });

    test('should return null when not loaded', () => {
      window.ModelHandler.isLoaded = false;
      window.ModelHandler.model = null;

      const info = window.ModelHandler.getModelInfo();

      expect(info).toBeNull();
    });
  });

  describe('cleanup', () => {
    test('should cleanup model resources', () => {
      const mockModel = {
        dispose: jest.fn()
      };
      
      window.ModelHandler.model = mockModel;
      window.ModelHandler.isLoaded = true;

      window.ModelHandler.cleanup();

      expect(mockModel.dispose).toHaveBeenCalled();
      expect(window.ModelHandler.model).toBeNull();
      expect(window.ModelHandler.isLoaded).toBe(false);
    });

    test('should handle cleanup when model has no dispose method', () => {
      window.ModelHandler.model = { predict: jest.fn() };
      window.ModelHandler.isLoaded = true;

      expect(() => window.ModelHandler.cleanup()).not.toThrow();
      expect(window.ModelHandler.model).toBeNull();
    });
  });

  describe('reload', () => {
    test('should cleanup and reinitialize model', async () => {
      const mockModel = {
        dispose: jest.fn(),
        predict: jest.fn()
      };
      
      window.ModelHandler.model = mockModel;
      window.ModelHandler.isLoaded = true;
      
      global.tmImage.load.mockResolvedValue(mockModel);

      await window.ModelHandler.reload();

      expect(mockModel.dispose).toHaveBeenCalled();
      expect(global.tmImage.load).toHaveBeenCalled();
      expect(window.ModelHandler.isLoaded).toBe(true);
    });
  });

  describe('getErrorMessage', () => {
    test('should return appropriate error messages', () => {
      const timeoutError = new Error('timeout occurred');
      const networkError = new Error('network failure');
      const genericError = new Error('something went wrong');

      expect(window.ModelHandler.getErrorMessage(timeoutError))
        .toContain('timeout');
      expect(window.ModelHandler.getErrorMessage(networkError))
        .toContain('network');
      expect(window.ModelHandler.getErrorMessage(genericError))
        .toBe(window.AppConfig.messages.errors.modelLoad);
    });
  });
});
