/* ==========================================================================
   Error Component Styles
   ========================================================================== */

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-2xl);
  background: rgba(244, 67, 54, 0.1);
  border: 2px solid var(--color-error);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
  text-align: center;
  opacity: 0;
  transform: scale(0.9);
  transition: all var(--transition-normal);
}

.error-container.visible {
  opacity: 1;
  transform: scale(1);
}

.error-icon {
  font-size: var(--font-size-4xl);
  animation: shake 0.5s ease-in-out;
}

.error-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-error);
  margin: 0;
}

.error-message {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  max-width: 400px;
  line-height: var(--line-height-relaxed);
}

/* Error types */
.error-container.network-error {
  border-color: var(--color-warning);
  background: rgba(255, 167, 38, 0.1);
}

.error-container.network-error .error-title {
  color: var(--color-warning);
}

.error-container.network-error .error-icon::before {
  content: '🌐';
}

.error-container.model-error {
  border-color: var(--color-error);
  background: rgba(244, 67, 54, 0.1);
}

.error-container.model-error .error-title {
  color: var(--color-error);
}

.error-container.model-error .error-icon::before {
  content: '🤖';
}

.error-container.file-error {
  border-color: var(--color-warning);
  background: rgba(255, 167, 38, 0.1);
}

.error-container.file-error .error-title {
  color: var(--color-warning);
}

.error-container.file-error .error-icon::before {
  content: '📁';
}

/* Error details */
.error-details {
  background: rgba(0, 0, 0, 0.2);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-md);
  font-family: 'Courier New', monospace;
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  text-align: left;
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  flex-wrap: wrap;
  justify-content: center;
}

/* Toast notifications */
.error-toast {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--color-error);
  color: var(--color-text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-tooltip);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  max-width: 300px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.error-toast.visible {
  transform: translateX(0);
}

.error-toast.success {
  background: var(--color-success);
}

.error-toast.warning {
  background: var(--color-warning);
}

.error-toast.info {
  background: var(--color-info);
}

/* Dismiss button */
.error-dismiss {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  background: none;
  border: none;
  color: inherit;
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

.error-dismiss:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* ==========================================================================
   Error Animations
   ========================================================================== */

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes errorSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.error-container {
  animation: errorSlideIn 0.4s ease-out;
}

/* ==========================================================================
   Error Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .error-container {
    padding: var(--spacing-xl);
    margin: 0 var(--spacing-md);
  }
  
  .error-toast {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .error-container {
    padding: var(--spacing-lg);
  }
  
  .error-icon {
    font-size: var(--font-size-3xl);
  }
  
  .error-title {
    font-size: var(--font-size-lg);
  }
  
  .error-message {
    font-size: var(--font-size-sm);
  }
}
