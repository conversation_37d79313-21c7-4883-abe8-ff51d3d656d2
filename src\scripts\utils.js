/**
 * Utility Functions
 * Seaweed Health Classifier
 */

window.Utils = {
  /**
   * Debounce function to limit the rate of function calls
   * @param {Function} func - Function to debounce
   * @param {number} wait - Wait time in milliseconds
   * @param {boolean} immediate - Execute immediately
   * @returns {Function} Debounced function
   */
  debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func.apply(this, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(this, args);
    };
  },

  /**
   * Throttle function to limit function execution frequency
   * @param {Function} func - Function to throttle
   * @param {number} limit - Time limit in milliseconds
   * @returns {Function} Throttled function
   */
  throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * Format file size in human readable format
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Validate image file
   * @param {File} file - File to validate
   * @returns {Object} Validation result
   */
  validateImageFile(file) {
    const config = window.AppConfig.ui;
    
    if (!file) {
      return { valid: false, error: 'No file selected' };
    }

    if (!config.supportedFormats.includes(file.type)) {
      return { 
        valid: false, 
        error: window.AppConfig.messages.errors.imageFormat 
      };
    }

    if (file.size > config.maxImageSize) {
      return { 
        valid: false, 
        error: window.AppConfig.messages.errors.imageSize 
      };
    }

    return { valid: true };
  },

  /**
   * Create image element from file
   * @param {File} file - Image file
   * @returns {Promise<HTMLImageElement>} Promise resolving to image element
   */
  createImageFromFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = e.target.result;
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  },

  /**
   * Resize image to fit within constraints
   * @param {HTMLImageElement} img - Image element
   * @param {number} maxWidth - Maximum width
   * @param {number} maxHeight - Maximum height
   * @returns {HTMLCanvasElement} Resized image canvas
   */
  resizeImage(img, maxWidth, maxHeight) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    let { width, height } = img;
    
    // Calculate new dimensions
    if (width > height) {
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
    } else {
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
    }
    
    canvas.width = width;
    canvas.height = height;
    
    ctx.drawImage(img, 0, 0, width, height);
    return canvas;
  },

  /**
   * Format percentage with proper precision
   * @param {number} value - Decimal value (0-1)
   * @param {number} precision - Decimal places
   * @returns {string} Formatted percentage
   */
  formatPercentage(value, precision = 1) {
    return (value * 100).toFixed(precision) + '%';
  },

  /**
   * Get confidence level based on value
   * @param {number} confidence - Confidence value (0-1)
   * @returns {string} Confidence level
   */
  getConfidenceLevel(confidence) {
    const thresholds = window.AppConfig.prediction.confidenceThreshold;
    if (confidence >= thresholds.high) return 'high';
    if (confidence >= thresholds.medium) return 'medium';
    return 'low';
  },

  /**
   * Generate unique ID
   * @returns {string} Unique identifier
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  /**
   * Log message with timestamp (development only)
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {*} data - Additional data
   */
  log(level, message, data = null) {
    if (!window.AppConfig.development.debug) return;
    
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    
    switch (level) {
      case 'error':
        console.error(logMessage, data);
        break;
      case 'warn':
        console.warn(logMessage, data);
        break;
      case 'info':
        console.info(logMessage, data);
        break;
      default:
        console.log(logMessage, data);
    }
  },

  /**
   * Show toast notification
   * @param {string} message - Toast message
   * @param {string} type - Toast type (success, error, warning, info)
   * @param {number} duration - Display duration in milliseconds
   */
  showToast(message, type = 'info', duration = null) {
    const toastDuration = duration || window.AppConfig.ui.toastDuration;
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `error-toast ${type}`;
    toast.textContent = message;
    
    // Add dismiss button
    const dismissBtn = document.createElement('button');
    dismissBtn.className = 'error-dismiss';
    dismissBtn.innerHTML = '×';
    dismissBtn.onclick = () => this.hideToast(toast);
    toast.appendChild(dismissBtn);
    
    // Add to DOM
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => toast.classList.add('visible'), 100);
    
    // Auto hide
    setTimeout(() => this.hideToast(toast), toastDuration);
    
    return toast;
  },

  /**
   * Hide toast notification
   * @param {HTMLElement} toast - Toast element
   */
  hideToast(toast) {
    toast.classList.remove('visible');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  },

  /**
   * Wait for specified time
   * @param {number} ms - Milliseconds to wait
   * @returns {Promise} Promise that resolves after delay
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Retry function with exponential backoff
   * @param {Function} fn - Function to retry
   * @param {number} maxRetries - Maximum retry attempts
   * @param {number} baseDelay - Base delay in milliseconds
   * @returns {Promise} Promise resolving to function result
   */
  async retry(fn, maxRetries = 3, baseDelay = 1000) {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        if (i === maxRetries) break;
        
        const delay = baseDelay * Math.pow(2, i);
        this.log('warn', `Retry attempt ${i + 1} failed, waiting ${delay}ms`, error);
        await this.wait(delay);
      }
    }
    
    throw lastError;
  }
};

// Freeze utils to prevent modifications
Object.freeze(window.Utils);
