/* ==========================================================================
   Button Component Styles
   ========================================================================== */

/* Enhanced Base button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--radius-2xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.8px;
  min-height: 56px;
  user-select: none;
  backdrop-filter: blur(8px);
  text-transform: uppercase;
  font-size: var(--font-size-base);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: translateY(-3px) scale(1.05);
}

.btn:not(:disabled):active {
  transform: translateY(-1px) scale(0.98);
}

/* Enhanced Primary button (predict button) */
.predict-button {
  background: var(--gradient-button);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-lg);
  min-width: 240px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.predict-button:not(:disabled):hover {
  background: var(--gradient-button-hover);
  box-shadow: var(--shadow-2xl);
  border-color: var(--color-primary-light);
}

.predict-button:not(:disabled):focus-visible {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* Secondary button (reset button) */
.reset-button {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border: 2px solid var(--color-border-secondary);
  backdrop-filter: blur(8px);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  min-width: 160px;
}

.reset-button:not(:disabled):hover {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-light);
}

/* Retry button (error state) */
.retry-button {
  background: var(--color-error);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
}

.retry-button:not(:disabled):hover {
  background: #d32f2f;
  box-shadow: var(--shadow-lg);
}

/* Button text and loader states */
.button-text,
.button-loader {
  transition: opacity var(--transition-fast);
}

.button-loader {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.button-loader::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Button loading state */
.btn.loading .button-text {
  opacity: 0;
}

.btn.loading .button-loader {
  opacity: 1;
}

.btn:not(.loading) .button-loader {
  opacity: 0;
  position: absolute;
}

/* ==========================================================================
   Enhanced Button Animations
   ========================================================================== */

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-4px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(4px);
  }
}

/* ==========================================================================
   Button Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .btn {
    width: 100%;
    max-width: 300px;
    padding: var(--spacing-lg) var(--spacing-xl);
  }
  
  .predict-button {
    min-width: unset;
  }
  
  .reset-button,
  .retry-button {
    min-width: unset;
  }
}

@media (max-width: 480px) {
  .btn {
    font-size: var(--font-size-base);
    padding: var(--spacing-md) var(--spacing-lg);
  }
}
