/**
 * UI Controller
 * Manages user interface interactions and state
 */

window.UIController = {
  elements: {},
  state: {
    currentImage: null,
    isAnalyzing: false,
    hasResults: false
  },

  /**
   * Initialize UI controller
   */
  init() {
    this.cacheElements();
    this.bindEvents();
    this.setupDragAndDrop();
    this.updateUI();
    
    window.Utils.log('info', 'UI Controller initialized');
  },

  /**
   * Cache DOM elements for better performance
   */
  cacheElements() {
    this.elements = {
      fileInput: document.getElementById('fileInput'),
      imagePreview: document.getElementById('image-preview'),
      predictButton: document.getElementById('predict-button'),
      resetButton: document.getElementById('reset-button'),
      resultsContainer: document.getElementById('results-container'),
      predictionsList: document.getElementById('predictions-list'),
      loadingIndicator: document.getElementById('loading-indicator'),
      errorContainer: document.getElementById('error-container'),
      errorMessage: document.getElementById('error-message'),
      retryButton: document.getElementById('retry-button'),
      uploadLabel: document.querySelector('.upload-label')
    };
  },

  /**
   * Bind event listeners
   */
  bindEvents() {
    const { fileInput, predictButton, resetButton, retryButton } = this.elements;

    // File input change
    fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

    // Predict button click
    predictButton.addEventListener('click', () => this.handlePredict());

    // Reset button click
    resetButton.addEventListener('click', () => this.handleReset());

    // Retry button click
    retryButton.addEventListener('click', () => this.handleRetry());

    // Keyboard accessibility
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));
  },

  /**
   * Setup drag and drop functionality
   */
  setupDragAndDrop() {
    if (!window.AppConfig.features.dragAndDrop) return;

    const { uploadLabel } = this.elements;
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      uploadLabel.addEventListener(eventName, this.preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
      uploadLabel.addEventListener(eventName, () => this.highlight(), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      uploadLabel.addEventListener(eventName, () => this.unhighlight(), false);
    });

    uploadLabel.addEventListener('drop', (e) => this.handleDrop(e), false);
  },

  /**
   * Prevent default drag behaviors
   * @param {Event} e - Event object
   */
  preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  },

  /**
   * Highlight drop zone
   */
  highlight() {
    this.elements.uploadLabel.classList.add('dragover');
  },

  /**
   * Remove highlight from drop zone
   */
  unhighlight() {
    this.elements.uploadLabel.classList.remove('dragover');
  },

  /**
   * Handle file drop
   * @param {DragEvent} e - Drop event
   */
  handleDrop(e) {
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      this.processFile(files[0]);
    }
  },

  /**
   * Handle file selection
   * @param {Event} e - Change event
   */
  handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
      this.processFile(file);
    }
  },

  /**
   * Process selected file
   * @param {File} file - Selected file
   */
  async processFile(file) {
    try {
      // Validate file
      const validation = window.Utils.validateImageFile(file);
      if (!validation.valid) {
        this.showError(validation.error, 'file-error');
        return;
      }

      // Clear previous results
      this.clearResults();
      this.hideError();

      // Create image element
      const imageElement = await window.Utils.createImageFromFile(file);
      
      // Update UI
      this.displayImage(imageElement, file);
      this.state.currentImage = imageElement;
      this.updateUI();

      window.Utils.log('info', 'File processed successfully', {
        name: file.name,
        size: window.Utils.formatFileSize(file.size),
        type: file.type
      });

    } catch (error) {
      window.Utils.log('error', 'File processing failed', error);
      this.showError(window.AppConfig.messages.errors.imageLoad, 'file-error');
    }
  },

  /**
   * Display image in preview
   * @param {HTMLImageElement} imageElement - Image element
   * @param {File} file - Original file
   */
  displayImage(imageElement, file) {
    const { imagePreview, uploadLabel } = this.elements;
    
    imagePreview.src = imageElement.src;
    imagePreview.alt = `Preview of ${file.name}`;
    imagePreview.style.display = 'block';
    
    // Add loaded class for animation
    setTimeout(() => {
      imagePreview.classList.add('loaded');
    }, 100);

    // Update upload label state
    uploadLabel.classList.add('has-file');
    uploadLabel.classList.remove('error');
  },

  /**
   * Handle predict button click
   */
  async handlePredict() {
    if (!this.state.currentImage || this.state.isAnalyzing) {
      return;
    }

    try {
      this.state.isAnalyzing = true;
      this.updateUI();

      // Show analyzing state
      this.showAnalyzing();

      // Make prediction
      const predictions = await window.ModelHandler.predict(this.state.currentImage);

      // Display results
      this.displayResults(predictions);
      this.state.hasResults = true;

      window.Utils.showToast(window.AppConfig.messages.success.predictionComplete, 'success');

    } catch (error) {
      window.Utils.log('error', 'Prediction failed', error);
      this.showError(window.AppConfig.messages.errors.prediction, 'model-error');
    } finally {
      this.state.isAnalyzing = false;
      this.hideAnalyzing();
      this.updateUI();
    }
  },

  /**
   * Show analyzing state
   */
  showAnalyzing() {
    const { imagePreview, predictButton } = this.elements;
    
    imagePreview.classList.add('analyzing');
    predictButton.classList.add('loading');
    predictButton.disabled = true;
  },

  /**
   * Hide analyzing state
   */
  hideAnalyzing() {
    const { imagePreview, predictButton } = this.elements;
    
    imagePreview.classList.remove('analyzing');
    predictButton.classList.remove('loading');
    predictButton.disabled = false;
  },

  /**
   * Display prediction results
   * @param {Array} predictions - Prediction results
   */
  displayResults(predictions) {
    const { resultsContainer, predictionsList } = this.elements;
    
    // Clear previous results
    predictionsList.innerHTML = '';

    // Create prediction items
    predictions.forEach((prediction, index) => {
      const item = this.createPredictionItem(prediction, index);
      predictionsList.appendChild(item);
    });

    // Show results container
    resultsContainer.style.display = 'block';
    setTimeout(() => {
      resultsContainer.classList.add('visible');
    }, 100);
  },

  /**
   * Create prediction item element
   * @param {Object} prediction - Prediction data
   * @param {number} index - Item index
   * @returns {HTMLElement} Prediction item element
   */
  createPredictionItem(prediction, index) {
    const item = document.createElement('div');
    item.className = `prediction-item ${prediction.confidence}-confidence`;
    item.style.animationDelay = `${index * 0.1}s`;

    item.innerHTML = `
      <div class="prediction-header">
        <span class="prediction-class">${prediction.className}</span>
        <span class="prediction-percentage">${prediction.percentage}</span>
      </div>
      <div class="prediction-bar">
        <div class="prediction-fill" style="width: ${prediction.probability * 100}%"></div>
      </div>
    `;

    return item;
  },

  /**
   * Handle reset button click
   */
  handleReset() {
    this.clearAll();
    this.updateUI();
    
    // Focus file input for accessibility
    this.elements.fileInput.focus();
  },

  /**
   * Handle retry button click
   */
  async handleRetry() {
    this.hideError();
    
    try {
      await window.ModelHandler.reload();
    } catch (error) {
      window.Utils.log('error', 'Retry failed', error);
    }
  },

  /**
   * Handle keyboard events
   * @param {KeyboardEvent} e - Keyboard event
   */
  handleKeyboard(e) {
    // Enter key on predict button
    if (e.key === 'Enter' && e.target === this.elements.predictButton) {
      this.handlePredict();
    }
    
    // Escape key to reset
    if (e.key === 'Escape') {
      this.handleReset();
    }
  },

  /**
   * Show loading indicator
   * @param {string} message - Loading message
   */
  showLoading(message = 'Loading...') {
    const { loadingIndicator } = this.elements;
    const loadingText = loadingIndicator.querySelector('.loading-text');
    
    if (loadingText) {
      loadingText.textContent = message;
    }
    
    loadingIndicator.style.display = 'flex';
    setTimeout(() => {
      loadingIndicator.classList.add('visible');
    }, 100);
  },

  /**
   * Hide loading indicator
   */
  hideLoading() {
    const { loadingIndicator } = this.elements;
    
    loadingIndicator.classList.remove('visible');
    setTimeout(() => {
      loadingIndicator.style.display = 'none';
    }, 300);
  },

  /**
   * Show error message
   * @param {string} message - Error message
   * @param {string} type - Error type
   */
  showError(message, type = 'generic') {
    const { errorContainer, errorMessage, uploadLabel } = this.elements;
    
    errorMessage.textContent = message;
    errorContainer.className = `error-container ${type}`;
    errorContainer.style.display = 'flex';
    
    setTimeout(() => {
      errorContainer.classList.add('visible');
    }, 100);

    // Update upload label state
    if (uploadLabel) {
      uploadLabel.classList.add('error');
      uploadLabel.classList.remove('has-file');
    }
  },

  /**
   * Hide error message
   */
  hideError() {
    const { errorContainer, uploadLabel } = this.elements;
    
    errorContainer.classList.remove('visible');
    setTimeout(() => {
      errorContainer.style.display = 'none';
    }, 300);

    // Reset upload label state
    if (uploadLabel) {
      uploadLabel.classList.remove('error');
    }
  },

  /**
   * Clear results
   */
  clearResults() {
    const { resultsContainer, predictionsList } = this.elements;
    
    resultsContainer.classList.remove('visible');
    setTimeout(() => {
      resultsContainer.style.display = 'none';
      predictionsList.innerHTML = '';
    }, 300);
    
    this.state.hasResults = false;
  },

  /**
   * Clear all UI state
   */
  clearAll() {
    const { fileInput, imagePreview, uploadLabel } = this.elements;
    
    // Reset file input
    fileInput.value = '';
    
    // Hide image preview
    imagePreview.style.display = 'none';
    imagePreview.classList.remove('loaded', 'analyzing', 'success', 'error');
    
    // Reset upload label
    uploadLabel.classList.remove('has-file', 'error', 'dragover');
    
    // Clear results and errors
    this.clearResults();
    this.hideError();
    this.hideLoading();
    
    // Reset state
    this.state.currentImage = null;
    this.state.isAnalyzing = false;
    this.state.hasResults = false;
  },

  /**
   * Update UI based on current state
   */
  updateUI() {
    const { predictButton } = this.elements;
    const { currentImage, isAnalyzing } = this.state;
    const modelReady = window.ModelHandler.isReady();
    
    // Update predict button
    predictButton.disabled = !currentImage || isAnalyzing || !modelReady;
    
    // Update button text
    const buttonText = predictButton.querySelector('.button-text');
    const buttonLoader = predictButton.querySelector('.button-loader');
    
    if (isAnalyzing) {
      buttonText.style.display = 'none';
      buttonLoader.style.display = 'flex';
    } else {
      buttonText.style.display = 'inline';
      buttonLoader.style.display = 'none';
    }
  }
};

// Initialize UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.UIController.init();
});

// Freeze UIController to prevent modifications
Object.freeze(window.UIController);
