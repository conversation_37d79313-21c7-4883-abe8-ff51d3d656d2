/**
 * <PERSON>rro<PERSON> Handler
 * Centralized error handling and reporting
 */

window.ErrorHandler = {
  // Error tracking
  errors: [],
  maxErrors: 50,
  
  // Error types
  ErrorTypes: {
    MODEL_LOAD: 'MODEL_LOAD',
    MODEL_PREDICT: 'MODEL_PREDICT',
    FILE_VALIDATION: 'FILE_VALIDATION',
    FILE_PROCESSING: 'FILE_PROCESSING',
    NETWORK: 'NETWORK',
    UI: 'UI',
    GENERIC: 'GENERIC'
  },

  /**
   * Initialize error handler
   */
  init() {
    this.setupGlobalHandlers();
    this.setupConsoleOverride();
    window.Utils.log('info', 'Error handler initialized');
  },

  /**
   * Setup global error handlers
   */
  setupGlobalHandlers() {
    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: this.ErrorTypes.GENERIC,
        message: 'Unhandled promise rejection',
        error: event.reason,
        stack: event.reason?.stack,
        timestamp: new Date().toISOString()
      });
      
      event.preventDefault();
    });

    // JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError({
        type: this.ErrorTypes.GENERIC,
        message: event.message,
        error: event.error,
        stack: event.error?.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: new Date().toISOString()
      });
    });

    // Resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleError({
          type: this.ErrorTypes.NETWORK,
          message: `Failed to load resource: ${event.target.src || event.target.href}`,
          element: event.target.tagName,
          timestamp: new Date().toISOString()
        });
      }
    }, true);
  },

  /**
   * Setup console override for development
   */
  setupConsoleOverride() {
    if (!window.AppConfig.development.debug) return;

    const originalError = console.error;
    console.error = (...args) => {
      this.handleError({
        type: this.ErrorTypes.GENERIC,
        message: args.join(' '),
        timestamp: new Date().toISOString(),
        source: 'console'
      });
      originalError.apply(console, args);
    };
  },

  /**
   * Handle error with context
   * @param {Object} errorInfo - Error information
   */
  handleError(errorInfo) {
    // Add to error log
    this.logError(errorInfo);
    
    // Show user-friendly message
    this.showUserError(errorInfo);
    
    // Report to analytics (if enabled)
    this.reportError(errorInfo);
    
    // Attempt recovery
    this.attemptRecovery(errorInfo);
  },

  /**
   * Log error to internal storage
   * @param {Object} errorInfo - Error information
   */
  logError(errorInfo) {
    // Add unique ID
    errorInfo.id = this.generateErrorId();
    
    // Add to errors array
    this.errors.unshift(errorInfo);
    
    // Limit error history
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }
    
    // Log to console in development
    if (window.AppConfig.development.debug) {
      console.group(`🚨 Error ${errorInfo.id}`);
      console.error('Type:', errorInfo.type);
      console.error('Message:', errorInfo.message);
      if (errorInfo.error) console.error('Error:', errorInfo.error);
      if (errorInfo.stack) console.error('Stack:', errorInfo.stack);
      console.groupEnd();
    }
  },

  /**
   * Show user-friendly error message
   * @param {Object} errorInfo - Error information
   */
  showUserError(errorInfo) {
    const userMessage = this.getUserMessage(errorInfo);
    const errorType = this.getUIErrorType(errorInfo.type);
    
    // Show toast for non-critical errors
    if (this.isNonCriticalError(errorInfo.type)) {
      window.Utils.showToast(userMessage, 'error');
      return;
    }
    
    // Show error container for critical errors
    if (window.UIController) {
      window.UIController.showError(userMessage, errorType);
    }
  },

  /**
   * Get user-friendly error message
   * @param {Object} errorInfo - Error information
   * @returns {string} User message
   */
  getUserMessage(errorInfo) {
    const messages = window.AppConfig.messages.errors;
    
    switch (errorInfo.type) {
      case this.ErrorTypes.MODEL_LOAD:
        return messages.modelLoad;
      case this.ErrorTypes.MODEL_PREDICT:
        return messages.prediction;
      case this.ErrorTypes.FILE_VALIDATION:
        return errorInfo.message || messages.imageFormat;
      case this.ErrorTypes.FILE_PROCESSING:
        return messages.imageLoad;
      case this.ErrorTypes.NETWORK:
        return messages.network;
      case this.ErrorTypes.UI:
        return errorInfo.message || 'Interface error occurred';
      default:
        return messages.generic;
    }
  },

  /**
   * Get UI error type for styling
   * @param {string} errorType - Error type
   * @returns {string} UI error type
   */
  getUIErrorType(errorType) {
    switch (errorType) {
      case this.ErrorTypes.MODEL_LOAD:
      case this.ErrorTypes.MODEL_PREDICT:
        return 'model-error';
      case this.ErrorTypes.FILE_VALIDATION:
      case this.ErrorTypes.FILE_PROCESSING:
        return 'file-error';
      case this.ErrorTypes.NETWORK:
        return 'network-error';
      default:
        return 'generic-error';
    }
  },

  /**
   * Check if error is non-critical (toast only)
   * @param {string} errorType - Error type
   * @returns {boolean} Is non-critical
   */
  isNonCriticalError(errorType) {
    return [
      this.ErrorTypes.FILE_VALIDATION,
      this.ErrorTypes.UI
    ].includes(errorType);
  },

  /**
   * Report error to analytics
   * @param {Object} errorInfo - Error information
   */
  reportError(errorInfo) {
    if (!window.AppConfig.analytics.enabled) return;
    
    // Report to analytics service
    try {
      // Example: Google Analytics event
      if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
          description: errorInfo.message,
          fatal: !this.isNonCriticalError(errorInfo.type)
        });
      }
    } catch (error) {
      console.warn('Failed to report error to analytics:', error);
    }
  },

  /**
   * Attempt error recovery
   * @param {Object} errorInfo - Error information
   */
  attemptRecovery(errorInfo) {
    switch (errorInfo.type) {
      case this.ErrorTypes.MODEL_LOAD:
        this.recoverModelLoad();
        break;
      case this.ErrorTypes.NETWORK:
        this.recoverNetwork();
        break;
      case this.ErrorTypes.UI:
        this.recoverUI();
        break;
    }
  },

  /**
   * Recover from model loading errors
   */
  recoverModelLoad() {
    // Retry model loading after delay
    setTimeout(() => {
      if (window.ModelHandler && !window.ModelHandler.isLoaded) {
        window.Utils.log('info', 'Attempting model recovery');
        window.ModelHandler.init().catch(() => {
          // Recovery failed, show manual retry option
          window.Utils.showToast(
            'Model loading failed. Please refresh the page.',
            'error',
            10000
          );
        });
      }
    }, 5000);
  },

  /**
   * Recover from network errors
   */
  recoverNetwork() {
    // Check network connectivity
    if (navigator.onLine) {
      window.Utils.log('info', 'Network appears to be back online');
      window.Utils.showToast('Network connection restored', 'success');
    }
  },

  /**
   * Recover from UI errors
   */
  recoverUI() {
    // Reset UI state
    if (window.UIController) {
      try {
        window.UIController.updateUI();
      } catch (error) {
        window.Utils.log('warn', 'UI recovery failed', error);
      }
    }
  },

  /**
   * Generate unique error ID
   * @returns {string} Error ID
   */
  generateErrorId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  },

  /**
   * Create error object
   * @param {string} type - Error type
   * @param {string} message - Error message
   * @param {Error} error - Original error object
   * @param {Object} context - Additional context
   * @returns {Object} Error object
   */
  createError(type, message, error = null, context = {}) {
    return {
      type,
      message,
      error,
      stack: error?.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
  },

  /**
   * Get error statistics
   * @returns {Object} Error statistics
   */
  getStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      recent: this.errors.slice(0, 5)
    };

    this.errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    });

    return stats;
  },

  /**
   * Clear error history
   */
  clearErrors() {
    this.errors = [];
    window.Utils.log('info', 'Error history cleared');
  },

  /**
   * Export errors for debugging
   * @returns {string} JSON string of errors
   */
  exportErrors() {
    return JSON.stringify(this.errors, null, 2);
  }
};

// Initialize error handler when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.ErrorHandler.init();
});

// Freeze ErrorHandler to prevent modifications
Object.freeze(window.ErrorHandler);
