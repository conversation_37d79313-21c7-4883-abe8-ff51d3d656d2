/* ==========================================================================
   Image Preview Component Styles
   ========================================================================== */

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.preview-image {
  max-width: 90vw;
  width: var(--image-max-width);
  max-height: var(--image-max-height);
  object-fit: cover;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  transition: all var(--transition-slow);
  border: 5px solid var(--color-border-secondary);
  backdrop-filter: blur(2px);
  opacity: 0;
  transform: scale(0.8);
}

.preview-image.loaded {
  opacity: 1;
  transform: scale(1);
}

.preview-image:hover {
  transform: scale(1.06);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

/* Preview states */
.preview-image.analyzing {
  filter: brightness(0.8);
  position: relative;
}

.preview-image.analyzing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 127, 115, 0.3);
  border-radius: inherit;
  animation: pulse 2s ease-in-out infinite;
}

.preview-image.error {
  border-color: var(--color-error);
  filter: grayscale(0.5);
}

.preview-image.success {
  border-color: var(--color-success);
}

/* Image loading placeholder */
.preview-placeholder {
  width: var(--image-max-width);
  height: var(--image-max-height);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-muted);
  font-size: var(--font-size-lg);
  backdrop-filter: blur(8px);
  border: 2px dashed var(--color-border-primary);
}

.preview-placeholder::before {
  content: '🖼️';
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-sm);
  display: block;
}

/* ==========================================================================
   Preview Animations
   ========================================================================== */

@keyframes imageLoad {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.preview-image.loading {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* ==========================================================================
   Preview Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .preview-image,
  .preview-placeholder {
    width: min(280px, 90vw);
    max-height: 280px;
  }
}

@media (max-width: 480px) {
  .preview-image,
  .preview-placeholder {
    width: min(240px, 90vw);
    max-height: 240px;
  }
  
  .preview-image:hover {
    transform: scale(1.02);
  }
}
