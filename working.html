<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="AI-powered seaweed health classification system using machine learning to analyze seaweed condition from images">
  <meta name="keywords" content="seaweed, health, classification, AI, machine learning, TensorFlow">
  <meta name="author" content="Seaweed Health Team">
  
  <title>Seaweed Health Classifier</title>
  
  <!-- Preconnect to external domains for better performance -->
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  <link rel="preconnect" href="https://teachablemachine.withgoogle.com">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Styles -->
  <link rel="stylesheet" href="src/styles/main.css">
</head>
<body>
  <!-- Header Section -->
  <header class="header">
    <h1 class="header__title">
      <span class="header__icon">🌿</span>
      Seaweed Condition Classifier
    </h1>

  </header>

  <!-- Main Application -->
  <main class="main">
    <!-- Upload Section -->
    <section class="upload-section">
      <div class="upload-container">
        <label for="fileInput" class="upload-label">
          <span class="upload-text">Choose Image</span>
          <span class="upload-hint">Select a seaweed image for analysis</span>
        </label>
        <input 
          type="file" 
          accept="image/*" 
          id="fileInput" 
          class="upload-input"
          aria-label="Upload seaweed image for classification"
        >
      </div>
    </section>

    <!-- Preview Section -->
    <section class="preview-section">
      <div class="preview-container">
        <img 
          id="image-preview" 
          class="preview-image" 
          alt="Uploaded seaweed image preview"
          style="display: none;"
        >
      </div>
    </section>

    <!-- Action Section -->
    <section class="action-section">
      <button 
        id="predict-button" 
        class="predict-button btn" 
        type="button"
        disabled
        aria-label="Start seaweed health analysis"
      >
        <span class="button-text">Analyze Health</span>
        <span class="button-loader" style="display: none;">Analyzing...</span>
      </button>
    </section>

    <!-- Results Section -->
    <section class="results-section">
      <div id="results-container" class="results-container" style="display: none;">
        <h2 class="results-title">Analysis Results</h2>
        <div id="predictions-list" class="predictions-list"></div>
        <div class="results-actions">
          <button id="reset-button" class="reset-button btn" type="button">
            Analyze Another Image
          </button>
        </div>
      </div>
    </section>

    <!-- Loading Section -->
    <section class="loading-section">
      <div id="loading-indicator" class="loading-indicator">
        <div class="loading-spinner"></div>
        <p class="loading-text">Loading AI model...</p>
      </div>
    </section>

    <!-- Error Section -->
    <section class="error-section">
      <div id="error-container" class="error-container" style="display: none;">
        <div class="error-icon">⚠️</div>
        <h3 class="error-title">Something went wrong</h3>
        <p id="error-message" class="error-message"></p>
        <button id="retry-button" class="retry-button btn" type="button">
          Try Again
        </button>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <p class="footer-text">
      Powered by <a href="https://teachablemachine.withgoogle.com/" target="_blank" rel="noopener">Teachable Machine</a> 
      and <a href="https://www.tensorflow.org/js" target="_blank" rel="noopener">TensorFlow.js</a>
    </p>
  </footer>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.9.0"></script>
  <script src="https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8.4/dist/teachablemachine-image.min.js"></script>
  
  <!-- Application Script -->
  <script src="app.js"></script>
</body>
</html>
