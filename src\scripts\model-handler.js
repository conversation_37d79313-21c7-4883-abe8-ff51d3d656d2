/**
 * Model Handler
 * Handles TensorFlow.js model loading and predictions
 */

window.ModelHandler = {
  model: null,
  isLoading: false,
  isLoaded: false,

  /**
   * Initialize and load the model
   * @returns {Promise<void>}
   */
  async init() {
    if (this.isLoaded || this.isLoading) {
      return;
    }

    this.isLoading = true;
    const config = window.AppConfig.model;

    try {
      window.Utils.log('info', 'Starting model initialization');
      
      // Show loading indicator
      window.UIController.showLoading('Loading AI model...');

      // Load model with retry logic
      await window.Utils.retry(
        () => this.loadModel(),
        config.maxRetries,
        config.retryDelay
      );

      this.isLoaded = true;
      this.isLoading = false;

      window.Utils.log('info', 'Model loaded successfully');
      window.Utils.showToast(window.AppConfig.messages.success.modelLoaded, 'success');
      
      // Hide loading indicator
      window.UIController.hideLoading();

    } catch (error) {
      this.isLoading = false;
      window.Utils.log('error', 'Failed to load model', error);
      
      const errorMessage = this.getErrorMessage(error);
      window.UIController.showError(errorMessage, 'model-error');
      
      throw error;
    }
  },

  /**
   * Load the TensorFlow.js model
   * @returns {Promise<void>}
   */
  async loadModel() {
    const config = window.AppConfig.model;
    const modelURL = config.url + config.modelFile;
    const metadataURL = config.url + config.metadataFile;

    // Check if tmImage is available
    if (typeof tmImage === 'undefined') {
      throw new Error('Teachable Machine library not loaded');
    }

    // Create timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Model loading timeout')), config.timeout);
    });

    // Load model with timeout
    const loadPromise = tmImage.load(modelURL, metadataURL);
    
    this.model = await Promise.race([loadPromise, timeoutPromise]);
    
    if (!this.model) {
      throw new Error('Model failed to load');
    }
  },

  /**
   * Make prediction on image
   * @param {HTMLImageElement} imageElement - Image to predict
   * @returns {Promise<Array>} Prediction results
   */
  async predict(imageElement) {
    if (!this.isLoaded || !this.model) {
      throw new Error('Model not loaded');
    }

    try {
      window.Utils.log('info', 'Starting prediction');
      
      // Ensure image is loaded
      await this.ensureImageLoaded(imageElement);

      // Make prediction
      const predictions = await this.model.predict(imageElement);
      
      // Process and sort predictions
      const processedPredictions = this.processPredictions(predictions);
      
      window.Utils.log('info', 'Prediction completed', processedPredictions);
      
      return processedPredictions;

    } catch (error) {
      window.Utils.log('error', 'Prediction failed', error);
      throw error;
    }
  },

  /**
   * Ensure image is fully loaded
   * @param {HTMLImageElement} imageElement - Image element
   * @returns {Promise<void>}
   */
  ensureImageLoaded(imageElement) {
    return new Promise((resolve, reject) => {
      if (imageElement.complete && imageElement.naturalHeight !== 0) {
        resolve();
      } else {
        imageElement.onload = () => resolve();
        imageElement.onerror = () => reject(new Error('Image failed to load'));
        
        // Timeout after 10 seconds
        setTimeout(() => reject(new Error('Image load timeout')), 10000);
      }
    });
  },

  /**
   * Process raw predictions from model
   * @param {Array} rawPredictions - Raw predictions from model
   * @returns {Array} Processed predictions
   */
  processPredictions(rawPredictions) {
    const config = window.AppConfig.prediction;
    
    let predictions = rawPredictions.map(prediction => ({
      className: prediction.className,
      probability: prediction.probability,
      confidence: window.Utils.getConfidenceLevel(prediction.probability),
      percentage: window.Utils.formatPercentage(prediction.probability)
    }));

    // Sort by confidence if enabled
    if (config.sortByConfidence) {
      predictions.sort((a, b) => b.probability - a.probability);
    }

    // Limit number of predictions
    if (config.maxPredictions > 0) {
      predictions = predictions.slice(0, config.maxPredictions);
    }

    return predictions;
  },

  /**
   * Get appropriate error message for different error types
   * @param {Error} error - Error object
   * @returns {string} User-friendly error message
   */
  getErrorMessage(error) {
    const messages = window.AppConfig.messages.errors;
    
    if (error.message.includes('timeout')) {
      return messages.modelTimeout;
    }
    
    if (error.message.includes('network') || error.message.includes('fetch')) {
      return messages.network;
    }
    
    if (error.message.includes('Teachable Machine')) {
      return 'Teachable Machine library failed to load. Please refresh the page.';
    }
    
    return messages.modelLoad;
  },

  /**
   * Check if model is ready for predictions
   * @returns {boolean} True if model is ready
   */
  isReady() {
    return this.isLoaded && this.model !== null;
  },

  /**
   * Get model information
   * @returns {Object} Model information
   */
  getModelInfo() {
    if (!this.isLoaded || !this.model) {
      return null;
    }

    return {
      loaded: this.isLoaded,
      modelUrl: window.AppConfig.model.url,
      version: window.AppConfig.version
    };
  },

  /**
   * Cleanup model resources
   */
  cleanup() {
    if (this.model && typeof this.model.dispose === 'function') {
      this.model.dispose();
    }
    
    this.model = null;
    this.isLoaded = false;
    this.isLoading = false;
    
    window.Utils.log('info', 'Model resources cleaned up');
  },

  /**
   * Reload model (useful for error recovery)
   * @returns {Promise<void>}
   */
  async reload() {
    window.Utils.log('info', 'Reloading model');
    
    this.cleanup();
    await this.init();
  }
};

// Initialize model when page loads
document.addEventListener('DOMContentLoaded', () => {
  // Only auto-load if not in development skip mode
  if (!window.AppConfig.development.skipModelLoad) {
    window.ModelHandler.init().catch(error => {
      window.Utils.log('error', 'Auto model initialization failed', error);
    });
  }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.ModelHandler.cleanup();
});

// Freeze ModelHandler to prevent modifications
Object.freeze(window.ModelHandler);
