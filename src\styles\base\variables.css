/* ==========================================================================
   CSS Custom Properties (Variables)
   ========================================================================== */

:root {
  /* ==========================================================================
     Color Palette
     ========================================================================== */
  
  /* Primary Colors - Enhanced Ocean/Seaweed Theme */
  --color-primary-dark: #002329;
  --color-primary: #006B5D;
  --color-primary-medium: #008A7A;
  --color-primary-light: #00A693;
  --color-primary-lighter: #1BC4B8;
  --color-secondary: #9BC53D;
  --color-secondary-light: #B8D65C;
  --color-accent: #4ECDC4;
  --color-accent-light: #7EDDD6;
  --color-accent-dark: #2B9B95;

  /* Enhanced UI Colors */
  --color-success: #00E676;
  --color-success-light: #69F0AE;
  --color-success-dark: #00C853;
  --color-warning: #FF9800;
  --color-warning-light: #FFB74D;
  --color-warning-dark: #F57C00;
  --color-error: #F44336;
  --color-error-light: #EF5350;
  --color-error-dark: #D32F2F;
  --color-info: #2196F3;
  --color-info-light: #64B5F6;
  --color-info-dark: #1976D2;
  
  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-muted: rgba(255, 255, 255, 0.6);
  --color-text-dark: #333333;
  
  /* Enhanced Background Colors */
  --color-bg-primary: rgba(0, 35, 41, 0.85);
  --color-bg-secondary: rgba(255, 255, 255, 0.12);
  --color-bg-tertiary: rgba(0, 0, 0, 0.4);
  --color-bg-light: rgba(255, 255, 255, 0.98);
  --color-bg-card: rgba(255, 255, 255, 0.95);
  --color-bg-overlay: rgba(0, 35, 41, 0.9);
  --color-bg-glass: rgba(255, 255, 255, 0.08);
  
  /* Border Colors */
  --color-border-primary: rgba(255, 255, 255, 0.2);
  --color-border-secondary: rgba(255, 255, 255, 0.3);
  --color-border-light: #ffffff;
  
  /* Focus Color */
  --color-focus: #00ffbf;
  
  /* ==========================================================================
     Enhanced Gradients
     ========================================================================== */

  --gradient-primary: linear-gradient(135deg, #006B5D 0%, #00A693 25%, #4ECDC4 50%, #9BC53D 75%, #B8D65C 100%);
  --gradient-secondary: linear-gradient(45deg, #4ECDC4 0%, #7EDDD6 100%);
  --gradient-button: linear-gradient(135deg, #006B5D 0%, #008A7A 50%, #00A693 100%);
  --gradient-button-hover: linear-gradient(135deg, #008A7A 0%, #00A693 50%, #1BC4B8 100%);
  --gradient-success: linear-gradient(135deg, #00C853 0%, #00E676 100%);
  --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  
  /* ==========================================================================
     Typography
     ========================================================================== */
  
  /* Font Families */
  --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 2rem;      /* 32px */
  --font-size-4xl: 2.5rem;    /* 40px */
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* ==========================================================================
     Spacing
     ========================================================================== */
  
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  
  /* ==========================================================================
     Layout
     ========================================================================== */
  
  --container-max-width: 1200px;
  --content-max-width: 500px;
  --image-max-width: 320px;
  --image-max-height: 320px;
  
  /* ==========================================================================
     Border Radius
     ========================================================================== */
  
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.25rem;   /* 20px */
  --radius-full: 9999px;
  
  /* ==========================================================================
     Enhanced Shadows
     ========================================================================== */

  --shadow-xs: 0 1px 2px rgba(0, 35, 41, 0.08);
  --shadow-sm: 0 2px 4px rgba(0, 35, 41, 0.12);
  --shadow-md: 0 4px 12px rgba(0, 35, 41, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 35, 41, 0.2);
  --shadow-xl: 0 16px 32px rgba(0, 35, 41, 0.25);
  --shadow-2xl: 0 24px 48px rgba(0, 35, 41, 0.3);
  --shadow-inset: inset 0 2px 8px rgba(255, 255, 255, 0.15);
  --shadow-glow: 0 0 20px rgba(78, 205, 196, 0.3);
  --shadow-success: 0 4px 12px rgba(0, 230, 118, 0.3);
  --shadow-error: 0 4px 12px rgba(244, 67, 54, 0.3);
  
  /* ==========================================================================
     Transitions
     ========================================================================== */
  
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* ==========================================================================
     Z-Index Scale
     ========================================================================== */
  
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}
