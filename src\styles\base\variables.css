/* ==========================================================================
   CSS Custom Properties (Variables)
   ========================================================================== */

:root {
  /* ==========================================================================
     Color Palette
     ========================================================================== */
  
  /* Primary Colors - Ocean/Seaweed Theme */
  --color-primary-dark: #003C43;
  --color-primary: #007F73;
  --color-primary-light: #135D66;
  --color-secondary: #A8CD9F;
  --color-accent: #627254;
  --color-accent-light: #76885B;
  
  /* UI Colors */
  --color-success: #00ffbf;
  --color-warning: #ffa726;
  --color-error: #f44336;
  --color-info: #2196f3;
  
  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-muted: rgba(255, 255, 255, 0.6);
  --color-text-dark: #333333;
  
  /* Background Colors */
  --color-bg-primary: rgba(0, 0, 0, 0.3);
  --color-bg-secondary: rgba(255, 255, 255, 0.1);
  --color-bg-tertiary: rgba(0, 0, 0, 0.25);
  --color-bg-light: rgba(255, 255, 255, 0.95);
  
  /* Border Colors */
  --color-border-primary: rgba(255, 255, 255, 0.2);
  --color-border-secondary: rgba(255, 255, 255, 0.3);
  --color-border-light: #ffffff;
  
  /* Focus Color */
  --color-focus: #00ffbf;
  
  /* ==========================================================================
     Gradients
     ========================================================================== */
  
  --gradient-primary: linear-gradient(270deg, #007F73, #A8CD9F, #627254, #76885B);
  --gradient-button: linear-gradient(to right, #003C43, #135D66);
  --gradient-button-hover: linear-gradient(to right, #135D66, #1B4242);
  
  /* ==========================================================================
     Typography
     ========================================================================== */
  
  /* Font Families */
  --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 2rem;      /* 32px */
  --font-size-4xl: 2.5rem;    /* 40px */
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* ==========================================================================
     Spacing
     ========================================================================== */
  
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  
  /* ==========================================================================
     Layout
     ========================================================================== */
  
  --container-max-width: 1200px;
  --content-max-width: 500px;
  --image-max-width: 320px;
  --image-max-height: 320px;
  
  /* ==========================================================================
     Border Radius
     ========================================================================== */
  
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.25rem;   /* 20px */
  --radius-full: 9999px;
  
  /* ==========================================================================
     Shadows
     ========================================================================== */
  
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 6px 20px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 12px 25px rgba(0, 0, 0, 0.4);
  --shadow-inset: inset 0 0 8px rgba(255, 255, 255, 0.2);
  
  /* ==========================================================================
     Transitions
     ========================================================================== */
  
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* ==========================================================================
     Z-Index Scale
     ========================================================================== */
  
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}
