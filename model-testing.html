<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Seaweed Health Model Testing - Accuracy visualization and performance analysis">
  <meta name="keywords" content="seaweed, model testing, accuracy, machine learning, TensorFlow, visualization">
  <meta name="author" content="Seaweed Health Research Team">
  
  <title>Seaweed Health Model Testing</title>
  
  <!-- Preconnect for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Chart.js for data visualization -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
  
  <!-- Styles -->
  <link rel="stylesheet" href="model-testing.css">
</head>
<body>
  <!-- Header Section -->
  <header class="header">
    <div class="header__container">
      <h1 class="header__title">
        <span class="header__icon">🧪</span>
        Seaweed Health Model Testing
      </h1>
      <p class="header__subtitle">
        Real-time accuracy visualization and performance analysis
      </p>
    </div>
  </header>

  <!-- Main Application -->
  <main class="main">
    <!-- Control Panel -->
    <section class="control-panel">
      <div class="control-panel__container">
        <div class="control-group">
          <label for="epochs" class="control-label">Training Epochs</label>
          <input type="number" id="epochs" class="control-input" value="100" min="10" max="500">
        </div>
        
        <div class="control-group">
          <label for="learning-rate" class="control-label">Learning Rate</label>
          <select id="learning-rate" class="control-select">
            <option value="0.001">0.001</option>
            <option value="0.01" selected>0.01</option>
            <option value="0.1">0.1</option>
          </select>
        </div>
        
        <div class="control-group">
          <label for="batch-size" class="control-label">Batch Size</label>
          <select id="batch-size" class="control-select">
            <option value="16">16</option>
            <option value="32" selected>32</option>
            <option value="64">64</option>
          </select>
        </div>
        
        <div class="control-actions">
          <button id="start-training" class="btn btn--primary">
            <span class="btn__text">Start Training</span>
            <span class="btn__loader" style="display: none;">Training...</span>
          </button>
          
          <button id="stop-training" class="btn btn--secondary" disabled>
            Stop Training
          </button>
          
          <button id="reset-training" class="btn btn--tertiary">
            Reset
          </button>
        </div>
      </div>
    </section>

    <!-- Charts Section -->
    <section class="charts-section">
      <div class="charts-container">
        <!-- Main Accuracy Chart -->
        <div class="chart-card chart-card--main">
          <div class="chart-header">
            <h2 class="chart-title">Accuracy and Accuracy Test per Epoch</h2>
            <div class="chart-controls">
              <button class="chart-control" data-action="download">📊</button>
              <button class="chart-control" data-action="fullscreen">⛶</button>
            </div>
          </div>
          <div class="chart-wrapper">
            <canvas id="accuracy-chart"></canvas>
          </div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color" style="background: #00ffbf;"></span>
              <span class="legend-label">Healthy</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #ff6b6b;"></span>
              <span class="legend-label">Noise</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #4ecdc4;"></span>
              <span class="legend-label">Ice-Ice</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #45b7d1;"></span>
              <span class="legend-label">Siren-Siren</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #f9ca24;"></span>
              <span class="legend-label">Ice-Ice & Siren-Siren</span>
            </div>
          </div>
        </div>

        <!-- Loss Chart -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">Training Loss</h3>
          </div>
          <div class="chart-wrapper">
            <canvas id="loss-chart"></canvas>
          </div>
        </div>

        <!-- Validation Accuracy Chart -->
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">Validation Accuracy</h3>
          </div>
          <div class="chart-wrapper">
            <canvas id="validation-chart"></canvas>
          </div>
        </div>
      </div>
    </section>

    <!-- Metrics Section -->
    <section class="metrics-section">
      <div class="metrics-container">
        <div class="metric-card">
          <div class="metric-icon">📈</div>
          <div class="metric-content">
            <div class="metric-label">Current Epoch</div>
            <div class="metric-value" id="current-epoch">0</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon">🎯</div>
          <div class="metric-content">
            <div class="metric-label">Best Accuracy</div>
            <div class="metric-value" id="best-accuracy">0.00%</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon">📊</div>
          <div class="metric-content">
            <div class="metric-label">Training Loss</div>
            <div class="metric-value" id="current-loss">0.000</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon">⏱️</div>
          <div class="metric-content">
            <div class="metric-label">Training Time</div>
            <div class="metric-value" id="training-time">00:00</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon">🔄</div>
          <div class="metric-content">
            <div class="metric-label">Status</div>
            <div class="metric-value" id="training-status">Ready</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Results Section -->
    <section class="results-section">
      <div class="results-container">
        <h2 class="results-title">Model Performance Summary</h2>
        
        <div class="results-grid">
          <div class="result-card" data-condition="healthy">
            <div class="result-header">
              <h3 class="result-title">Healthy</h3>
              <div class="result-status result-status--good">Excellent</div>
            </div>
            <div class="result-metrics">
              <div class="result-metric">
                <span class="result-metric-label">Accuracy:</span>
                <span class="result-metric-value" id="healthy-accuracy">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Precision:</span>
                <span class="result-metric-value" id="healthy-precision">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Recall:</span>
                <span class="result-metric-value" id="healthy-recall">0.00%</span>
              </div>
            </div>
          </div>
          
          <div class="result-card" data-condition="noise">
            <div class="result-header">
              <h3 class="result-title">Noise</h3>
              <div class="result-status result-status--warning">Good</div>
            </div>
            <div class="result-metrics">
              <div class="result-metric">
                <span class="result-metric-label">Accuracy:</span>
                <span class="result-metric-value" id="noise-accuracy">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Precision:</span>
                <span class="result-metric-value" id="noise-precision">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Recall:</span>
                <span class="result-metric-value" id="noise-recall">0.00%</span>
              </div>
            </div>
          </div>
          
          <div class="result-card" data-condition="ice-ice">
            <div class="result-header">
              <h3 class="result-title">Ice-Ice</h3>
              <div class="result-status result-status--good">Excellent</div>
            </div>
            <div class="result-metrics">
              <div class="result-metric">
                <span class="result-metric-label">Accuracy:</span>
                <span class="result-metric-value" id="ice-ice-accuracy">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Precision:</span>
                <span class="result-metric-value" id="ice-ice-precision">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Recall:</span>
                <span class="result-metric-value" id="ice-ice-recall">0.00%</span>
              </div>
            </div>
          </div>
          
          <div class="result-card" data-condition="siren-siren">
            <div class="result-header">
              <h3 class="result-title">Siren-Siren</h3>
              <div class="result-status result-status--good">Good</div>
            </div>
            <div class="result-metrics">
              <div class="result-metric">
                <span class="result-metric-label">Accuracy:</span>
                <span class="result-metric-value" id="siren-siren-accuracy">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Precision:</span>
                <span class="result-metric-value" id="siren-siren-precision">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Recall:</span>
                <span class="result-metric-value" id="siren-siren-recall">0.00%</span>
              </div>
            </div>
          </div>
          
          <div class="result-card" data-condition="combined">
            <div class="result-header">
              <h3 class="result-title">Ice-Ice & Siren-Siren</h3>
              <div class="result-status result-status--warning">Fair</div>
            </div>
            <div class="result-metrics">
              <div class="result-metric">
                <span class="result-metric-label">Accuracy:</span>
                <span class="result-metric-value" id="combined-accuracy">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Precision:</span>
                <span class="result-metric-value" id="combined-precision">0.00%</span>
              </div>
              <div class="result-metric">
                <span class="result-metric-label">Recall:</span>
                <span class="result-metric-value" id="combined-recall">0.00%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Progress Section -->
    <section class="progress-section">
      <div class="progress-container">
        <div class="progress-header">
          <h3 class="progress-title">Training Progress</h3>
          <span class="progress-percentage" id="progress-percentage">0%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="progress-info">
          <span class="progress-text" id="progress-text">Ready to start training</span>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
      <p class="footer-text">
        Seaweed Health Model Testing System - 
        <a href="#" target="_blank" rel="noopener">Research Documentation</a> | 
        <a href="#" target="_blank" rel="noopener">API Reference</a>
      </p>
    </div>
  </footer>

  <!-- Application Script -->
  <script src="model-testing.js"></script>
</body>
</html>
