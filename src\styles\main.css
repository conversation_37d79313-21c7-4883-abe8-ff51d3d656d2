/* ==========================================================================
   Seaweed Health Classifier - Main Stylesheet
   ========================================================================== */

/* Import all component styles */
@import url('./base/reset.css');
@import url('./base/variables.css');
@import url('./base/typography.css');
@import url('./components/header.css');
@import url('./components/upload.css');
@import url('./components/preview.css');
@import url('./components/buttons.css');
@import url('./components/results.css');
@import url('./components/loading.css');
@import url('./components/error.css');
@import url('./components/footer.css');
@import url('./utilities/animations.css');
@import url('./utilities/responsive.css');

/* ==========================================================================
   Main Layout
   ========================================================================== */

body {
  background: var(--gradient-primary);
  background-size: 800% 800%;
  animation: gradientFlow 20s ease infinite;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) var(--spacing-md);
  color: var(--color-text-primary);
  text-align: center;
  overflow-x: hidden;
  font-family: var(--font-primary);
}

.main {
  width: 100%;
  max-width: var(--container-max-width);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
}

/* ==========================================================================
   Section Layouts
   ========================================================================== */

.upload-section,
.preview-section,
.action-section,
.results-section,
.loading-section,
.error-section {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-container,
.preview-container,
.results-container,
.loading-indicator,
.error-container {
  width: 100%;
  max-width: var(--content-max-width);
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.hidden {
  display: none !important;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

/* ==========================================================================
   Focus Management
   ========================================================================== */

*:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}
