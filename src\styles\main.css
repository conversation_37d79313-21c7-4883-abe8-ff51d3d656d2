/* ==========================================================================
   Seaweed Health Classifier - Main Stylesheet
   ========================================================================== */

/* ==========================================================================
   CSS Reset and Normalize
   ========================================================================== */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  line-height: 1.5;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove button styling */
button {
  background: none;
  border: none;
  cursor: pointer;
}

/* Remove input styling */
input {
  border: none;
  outline: none;
}

/* ==========================================================================
   CSS Custom Properties (Variables)
   ========================================================================== */

:root {
  /* Colors */
  --color-primary-dark: #003C43;
  --color-primary: #007F73;
  --color-primary-light: #135D66;
  --color-secondary: #A8CD9F;
  --color-accent: #627254;
  --color-accent-light: #76885B;

  --color-success: #00ffbf;
  --color-warning: #ffa726;
  --color-error: #f44336;
  --color-info: #2196f3;

  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-muted: rgba(255, 255, 255, 0.6);
  --color-text-dark: #333333;

  --color-bg-primary: rgba(0, 0, 0, 0.3);
  --color-bg-secondary: rgba(255, 255, 255, 0.1);
  --color-bg-tertiary: rgba(0, 0, 0, 0.25);
  --color-bg-light: rgba(255, 255, 255, 0.95);

  --color-border-primary: rgba(255, 255, 255, 0.2);
  --color-border-secondary: rgba(255, 255, 255, 0.3);
  --color-border-light: #ffffff;

  --color-focus: #00ffbf;

  /* Gradients */
  --gradient-primary: linear-gradient(270deg, #007F73, #A8CD9F, #627254, #76885B);
  --gradient-button: linear-gradient(to right, #003C43, #135D66);
  --gradient-button-hover: linear-gradient(to right, #135D66, #1B4242);

  /* Typography */
  --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 2.5rem;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Layout */
  --container-max-width: 1200px;
  --content-max-width: 500px;
  --image-max-width: 320px;
  --image-max-height: 320px;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.25rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 6px 20px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 12px 25px rgba(0, 0, 0, 0.4);
  --shadow-inset: inset 0 0 8px rgba(255, 255, 255, 0.2);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ==========================================================================
   Typography Styles
   ========================================================================== */

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-2xl);
}

p {
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-relaxed);
}

a {
  color: var(--color-success);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover,
a:focus {
  color: var(--color-text-primary);
  text-decoration: underline;
}

/* ==========================================================================
   Keyframe Animations
   ========================================================================== */

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==========================================================================
   Main Layout
   ========================================================================== */

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;
}

body {
  background: var(--gradient-primary);
  background-size: 400% 400%;
  animation: gradientFlow 25s ease infinite;
  height: 100vh;
  color: var(--color-text-primary);
  font-family: var(--font-primary);
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(155, 197, 61, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* ==========================================================================
   Compact Grid Layout System
   ========================================================================== */

.app-container {
  height: 100vh;
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header"
    "main"
    "footer";
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  box-sizing: border-box;
}

.header-compact {
  grid-area: header;
  text-align: center;
  z-index: 10;
}

.main-grid {
  grid-area: main;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: var(--spacing-lg);
  min-height: 100vh;
  overflow-y: auto;
  width: 100%;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 600px;
  width: 100%;
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: fit-content;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .left-panel {
    max-width: 100%;
    margin: 0 var(--spacing-md);
    padding: var(--spacing-lg);
  }
}

/* Right panel removed for centered single-column layout */

.footer-compact {
  grid-area: footer;
  text-align: center;
  z-index: 10;
}

/* ==========================================================================
   Compact Component Styles
   ========================================================================== */

/* Header Compact */
.header__title-compact {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-xl);
  background: var(--gradient-card);
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-normal);
  color: var(--color-text-dark);
}

.header__title-compact:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.header__title-compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.8s ease;
}

.header__title-compact:hover::before {
  left: 100%;
}

/* Upload Section Compact */
.upload-section-compact {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.upload-container-compact {
  width: 100%;
  display: flex;
  justify-content: center;
}

.upload-label-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border: 2px dashed var(--color-primary-light);
  border-radius: var(--radius-xl);
  background: var(--gradient-card);
  color: var(--color-text-dark);
  cursor: pointer;
  transition: all var(--transition-normal);
  width: 100%;
  max-width: 280px;
  min-height: 120px;
  text-align: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-md);
}

.upload-label-compact::before {
  content: '📁';
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-xs);
  display: block;
  transition: all var(--transition-normal);
  position: relative;
  z-index: 1;
}

/* Upload states for compact */
.upload-label-compact.dragover {
  background: var(--gradient-glass);
  border-color: var(--color-primary);
  border-width: 3px;
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-glow);
  animation: dragPulse 0.6s ease-in-out infinite alternate;
}

.upload-label-compact.has-file {
  background: var(--gradient-card);
  border-color: var(--color-success);
  border-width: 2px;
  box-shadow: var(--shadow-success);
  animation: uploadSuccess 0.6s ease-out;
}

.upload-label-compact.has-file::before {
  content: '✅';
  animation: bounce 0.6s ease;
}

.upload-label-compact.error {
  background: var(--gradient-card);
  border-color: var(--color-error);
  border-width: 2px;
  box-shadow: var(--shadow-error);
  animation: shake 0.5s ease-in-out;
}

.upload-label-compact.error::before {
  content: '❌';
  animation: shake 0.5s ease;
}

.upload-label-compact:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.upload-label-compact:hover::before {
  transform: scale(1.1) rotate(5deg);
}

/* Preview Section Compact */
.preview-section-compact {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
}

.preview-container-compact {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image-compact {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  border: 2px solid var(--color-primary-light);
  backdrop-filter: blur(4px);
  opacity: 0;
  transform: scale(0.9);
}

.preview-image-compact.loaded {
  opacity: 1;
  transform: scale(1);
  animation: imageLoad 0.6s ease-out;
}

.preview-image-compact:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-xl);
}

/* Action Section Compact */
.action-section-compact {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: var(--spacing-md) 0;
}

.predict-button-compact {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  min-height: 48px;
  user-select: none;
  backdrop-filter: blur(8px);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--gradient-button);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-md);
  width: 100%;
  max-width: 200px;
}

.predict-button-compact:not(:disabled):hover {
  background: var(--gradient-button-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px) scale(1.02);
}

.predict-button-compact:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Results Section Compact */
.results-section-compact {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.results-container-compact {
  background: var(--gradient-card);
  padding: var(--spacing-lg);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(16px);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border-secondary);
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  transition: all var(--transition-slow);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.results-container-compact.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Chart Section Compact */
.accuracy-chart-compact {
  flex: 0 0 auto;
}

.chart-header-compact {
  text-align: center;
  margin-bottom: var(--spacing-sm);
}

.chart-title-compact {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-dark);
  margin: 0;
}

.chart-content-compact {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm);
}

/* Predictions List Compact */
.predictions-list-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  overflow-y: auto;
  max-height: 200px;
  padding-right: var(--spacing-xs);
}

.predictions-list-compact::-webkit-scrollbar {
  width: 4px;
}

.predictions-list-compact::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
}

.predictions-list-compact::-webkit-scrollbar-thumb {
  background: var(--color-primary-light);
  border-radius: var(--radius-sm);
}

/* Compact Prediction Items */
.prediction-item-compact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-bg-glass);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-primary);
  margin-bottom: var(--spacing-xs);
}

.prediction-item-compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--gradient-success);
  transition: width var(--transition-normal);
}

.prediction-item-compact:hover {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary-light);
}

.prediction-item-compact:hover::before {
  width: 6px;
}

.prediction-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-dark);
}

.prediction-class-compact {
  font-weight: var(--font-weight-semibold);
  flex: 1;
  text-align: left;
}

.prediction-percentage-compact {
  font-weight: var(--font-weight-bold);
  color: var(--color-success);
  font-size: var(--font-size-sm);
}

.prediction-bar-compact {
  height: 6px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.prediction-fill-compact {
  height: 100%;
  background: var(--gradient-success);
  border-radius: var(--radius-full);
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 230, 118, 0.3);
}

/* Results Actions Compact */
.results-actions-compact {
  flex: 0 0 auto;
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-sm);
}

.reset-button-compact {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-secondary);
  backdrop-filter: blur(8px);
}

.reset-button-compact:hover {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-light);
  transform: translateY(-1px);
}

/* Loading Section Compact */
.loading-section-compact {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 35, 41, 0.9);
  backdrop-filter: blur(8px);
  border-radius: var(--radius-xl);
  z-index: 100;
}

.loading-indicator-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--gradient-card);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(16px);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border-secondary);
  opacity: 0;
  transform: scale(0.9);
  transition: all var(--transition-normal);
}

.loading-indicator-compact.visible {
  opacity: 1;
  transform: scale(1);
}

.loading-spinner-compact {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.15);
  border-top: 3px solid var(--color-primary);
  border-right: 3px solid var(--color-accent);
  border-radius: 50%;
  animation: spinEnhanced 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 107, 93, 0.3));
}

.loading-text-compact {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-dark);
  text-align: center;
  margin: 0;
}

/* Error Section Compact */
.error-section-compact {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 35, 41, 0.9);
  backdrop-filter: blur(8px);
  border-radius: var(--radius-xl);
  z-index: 100;
}

.error-container-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--gradient-card);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(16px);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-error);
  text-align: center;
  max-width: 300px;
}

.error-icon-compact {
  font-size: var(--font-size-2xl);
}

.error-title-compact {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-dark);
  margin: 0;
}

.error-message-compact {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.retry-button-compact {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  background: var(--color-error);
  color: white;
  box-shadow: var(--shadow-md);
}

.retry-button-compact:hover {
  background: var(--color-error-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Footer Compact */
.footer-compact {
  padding: var(--spacing-sm);
}

.footer-text-compact {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
}

.footer-text-compact a {
  color: var(--color-success);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-text-compact a:hover {
  color: var(--color-text-primary);
}

/* ==========================================================================
   Compact Layout Responsive Design
   ========================================================================== */

/* Large screens (1200px+) */
@media (min-width: 1200px) {
  .main-grid {
    gap: var(--spacing-xl);
  }

  .upload-label-compact {
    max-width: 320px;
    min-height: 140px;
  }

  .chart-content-compact {
    height: 180px;
  }

  .predictions-list-compact {
    max-height: 250px;
  }
}

/* Medium screens (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 768px) {
  .app-container {
    padding: var(--spacing-sm);
  }

  .main-grid {
    gap: var(--spacing-md);
  }

  .upload-label-compact {
    max-width: 260px;
    min-height: 100px;
    padding: var(--spacing-md);
  }

  .chart-content-compact {
    height: 120px;
  }

  .predictions-list-compact {
    max-height: 180px;
  }

  .header__title-compact {
    font-size: var(--font-size-lg);
  }
}

/* Small screens (480px - 767px) - Stack vertically */
@media (max-width: 767px) {
  .app-container {
    padding: var(--spacing-xs);
    gap: var(--spacing-xs);
  }

  .main-grid {
    gap: var(--spacing-sm);
  }

  .left-panel {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto;
    gap: var(--spacing-sm);
  }

  .upload-section-compact {
    grid-column: 1;
  }

  .preview-section-compact {
    grid-column: 2;
    min-height: 120px;
  }

  .action-section-compact {
    grid-column: 3;
  }

  .upload-label-compact {
    max-width: none;
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-sm);
  }

  .upload-label-compact::before {
    font-size: var(--font-size-lg);
  }

  .predict-button-compact {
    max-width: none;
    width: 100%;
    min-height: 40px;
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm);
  }

  .chart-content-compact {
    height: 100px;
  }

  .predictions-list-compact {
    max-height: 150px;
  }

  .header__title-compact {
    font-size: var(--font-size-base);
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .results-container-compact {
    padding: var(--spacing-sm);
  }
}

/* Extra small screens (below 480px) */
@media (max-width: 479px) {
  .app-container {
    padding: var(--spacing-xs);
  }

  .left-panel {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: var(--spacing-xs);
  }

  .upload-section-compact,
  .preview-section-compact,
  .action-section-compact {
    grid-column: 1;
  }

  .preview-section-compact {
    min-height: 100px;
  }

  .upload-label-compact {
    min-height: 60px;
    padding: var(--spacing-xs);
  }

  .upload-text {
    font-size: var(--font-size-sm);
  }

  .upload-hint {
    font-size: var(--font-size-xs);
  }

  .predict-button-compact {
    min-height: 36px;
    font-size: var(--font-size-xs);
  }

  .chart-content-compact {
    height: 80px;
  }

  .predictions-list-compact {
    max-height: 120px;
  }

  .header__title-compact {
    font-size: var(--font-size-sm);
  }
}

/* ==========================================================================
   Utility Classes for Compact Layout
   ========================================================================== */

/* Hide elements when not needed */
.hidden {
  display: none !important;
}

.invisible {
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Show/hide states for different sections */
.app-container.uploading .right-panel > *:not(.loading-section-compact) {
  opacity: 0.3;
  pointer-events: none;
}

.app-container.analyzing .left-panel .upload-section-compact,
.app-container.analyzing .left-panel .action-section-compact {
  opacity: 0.5;
  pointer-events: none;
}

.app-container.results-ready .loading-section-compact,
.app-container.results-ready .error-section-compact {
  display: none;
}

/* Smooth transitions for state changes */
.left-panel > *,
.right-panel > * {
  transition: opacity var(--transition-normal), transform var(--transition-normal);
}

/* Ensure proper overflow handling in containers */
.app-container,
.main-grid,
.left-panel,
.right-panel,
.results-container-compact {
  overflow: visible;
}

/* Force height constraints */
.main-grid {
  min-height: 0; /* Allow grid items to shrink */
}

.left-panel,
.right-panel {
  min-height: 0; /* Allow flex items to shrink */
}

/* Compact text styles */
.upload-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  position: relative;
  z-index: 1;
}

.upload-hint {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  opacity: 0.8;
  position: relative;
  z-index: 1;
}

/* ==========================================================================
   Section Layouts
   ========================================================================== */

.upload-section,
.preview-section,
.action-section,
.results-section,
.loading-section,
.error-section {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-container,
.preview-container,
.results-container,
.loading-indicator,
.error-container {
  width: 100%;
  max-width: var(--content-max-width);
}

/* ==========================================================================
   Header Component Styles
   ========================================================================== */

.header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  max-width: 700px;
}

.header__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-2xl);
  background: var(--color-bg-primary);
  backdrop-filter: blur(6px);
  box-shadow: var(--shadow-lg);
  line-height: var(--line-height-relaxed);
  transition: transform var(--transition-normal);
  border: 1px solid var(--color-border-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.header__title:hover {
  transform: scale(1.02);
}

.header__icon {
  font-size: 1.2em;
  display: inline-block;
  animation: float 3s ease-in-out infinite;
}

.header__subtitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin: 0;
  opacity: 0.9;
}

/* ==========================================================================
   Upload Component Styles
   ========================================================================== */

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xl) var(--spacing-2xl);
  border: 2px dashed var(--color-border-light);
  border-radius: var(--radius-xl);
  background: var(--color-bg-light);
  color: var(--color-text-dark);
  cursor: pointer;
  transition: all var(--transition-normal);
  min-width: 280px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.upload-label:hover {
  background: rgba(255, 255, 255, 0.98);
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.upload-label:focus-within {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.upload-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

.upload-hint {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  font-weight: var(--font-weight-normal);
}

.upload-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.upload-label::before {
  content: '📁';
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-sm);
  display: block;
  transition: transform var(--transition-normal);
}

.upload-label:hover::before {
  transform: scale(1.1);
}

.upload-label.has-file::before {
  content: '✅';
}

.upload-label.error::before {
  content: '❌';
}

.upload-label.dragover {
  background: rgba(0, 127, 115, 0.1);
  border-color: var(--color-primary);
  transform: scale(1.02);
}

.upload-label.has-file {
  background: rgba(0, 255, 191, 0.1);
  border-color: var(--color-success);
}

.upload-label.has-file .upload-text {
  color: var(--color-success);
}

.upload-label.error {
  background: rgba(244, 67, 54, 0.1);
  border-color: var(--color-error);
}

.upload-label.error .upload-text {
  color: var(--color-error);
}

/* ==========================================================================
   Image Preview Component Styles
   ========================================================================== */

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.preview-image {
  max-width: 90vw;
  width: var(--image-max-width);
  max-height: var(--image-max-height);
  object-fit: cover;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  transition: all var(--transition-slow);
  border: 5px solid var(--color-border-secondary);
  backdrop-filter: blur(2px);
  opacity: 0;
  transform: scale(0.8);
}

.preview-image.loaded {
  opacity: 1;
  transform: scale(1);
}

.preview-image:hover {
  transform: scale(1.06);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.preview-image.analyzing {
  filter: brightness(0.8);
  position: relative;
}

.preview-image.analyzing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 127, 115, 0.3);
  border-radius: inherit;
  animation: pulse 2s ease-in-out infinite;
}

/* ==========================================================================
   Button Component Styles
   ========================================================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-full);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  min-height: 48px;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: scale(1.05);
}

.btn:not(:disabled):active {
  transform: scale(0.98);
}

.predict-button {
  background: var(--gradient-button);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
}

.predict-button:not(:disabled):hover {
  background: var(--gradient-button-hover);
  box-shadow: var(--shadow-xl);
}

.predict-button:not(:disabled):focus-visible {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.reset-button {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border: 2px solid var(--color-border-secondary);
  backdrop-filter: blur(8px);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  min-width: 160px;
}

.reset-button:not(:disabled):hover {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-light);
}

.retry-button {
  background: var(--color-error);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
}

.retry-button:not(:disabled):hover {
  background: #d32f2f;
  box-shadow: var(--shadow-lg);
}

.button-text,
.button-loader {
  transition: opacity var(--transition-fast);
}

.button-loader {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.button-loader::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.btn.loading .button-text {
  opacity: 0;
}

.btn.loading .button-loader {
  opacity: 1;
}

.btn:not(.loading) .button-loader {
  opacity: 0;
  position: absolute;
}

/* ==========================================================================
   Results Component Styles
   ========================================================================== */

.results-container {
  background: var(--color-bg-secondary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-primary);
  opacity: 0;
  transform: translateY(20px);
}

.results-container.visible {
  opacity: 1;
  transform: translateY(0);
}

.results-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.predictions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.prediction-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(4px);
  box-shadow: var(--shadow-inset);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.prediction-item:hover {
  transform: translateX(4px);
  background: rgba(0, 0, 0, 0.35);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.prediction-class {
  font-weight: var(--font-weight-semibold);
}

.prediction-percentage {
  font-weight: var(--font-weight-bold);
  color: var(--color-success);
}

.prediction-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.prediction-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success), var(--color-primary));
  border-radius: var(--radius-sm);
  transition: width 0.8s ease-out;
  position: relative;
}

.prediction-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.prediction-item.high-confidence .prediction-percentage {
  color: var(--color-success);
}

.prediction-item.medium-confidence .prediction-percentage {
  color: var(--color-warning);
}

.prediction-item.low-confidence .prediction-percentage {
  color: var(--color-error);
}

.prediction-item.high-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-success), #00cc99);
}

.prediction-item.medium-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-warning), #ffb74d);
}

.prediction-item.low-confidence .prediction-fill {
  background: linear-gradient(90deg, var(--color-error), #ef5350);
}

/* ==========================================================================
   Accuracy Chart Styles
   ========================================================================== */

.accuracy-chart {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
  opacity: 1;
  transform: translateY(0);
  /* Temporarily disable animation for debugging */
  /* animation: slideInUp var(--transition-normal) ease-out forwards;
  animation-delay: 0.2s; */
}

.chart-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border-primary);
}

.chart-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.chart-icon {
  font-size: 1.2em;
}

.chart-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-normal);
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  position: relative;
  height: 300px;
  width: 100%;
  justify-content: center;
  align-items: center;
}

#accuracy-line-chart {
  width: 100% !important;
  height: 100% !important;
  max-height: 300px;
}

.chart-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-md);
  border-left: 4px solid transparent;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.chart-row:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
}

.chart-row.rank-1 {
  border-left-color: var(--color-success);
  background: rgba(0, 255, 191, 0.1);
}

.chart-row.rank-2 {
  border-left-color: var(--color-warning);
  background: rgba(255, 167, 38, 0.1);
}

.chart-row.rank-3 {
  border-left-color: var(--color-info);
  background: rgba(33, 150, 243, 0.1);
}

.chart-row.high-confidence {
  border-left-color: var(--color-success);
}

.chart-row.medium-confidence {
  border-left-color: var(--color-warning);
}

.chart-row.low-confidence {
  border-left-color: var(--color-error);
}

.chart-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.chart-rank {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: white;
  background: var(--color-primary);
}

.chart-rank.rank-1 {
  background: var(--color-success);
}

.chart-rank.rank-2 {
  background: var(--color-warning);
}

.chart-rank.rank-3 {
  background: var(--color-info);
}

.chart-class-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.chart-accuracy {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.chart-percentage {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  min-width: 60px;
  text-align: right;
}

.chart-percentage.high-confidence {
  color: var(--color-success);
}

.chart-percentage.medium-confidence {
  color: var(--color-warning);
}

.chart-percentage.low-confidence {
  color: var(--color-error);
}

.chart-confidence-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chart-confidence-badge.high-confidence {
  background: rgba(0, 255, 191, 0.2);
  color: var(--color-success);
  border: 1px solid rgba(0, 255, 191, 0.3);
}

.chart-confidence-badge.medium-confidence {
  background: rgba(255, 167, 38, 0.2);
  color: var(--color-warning);
  border: 1px solid rgba(255, 167, 38, 0.3);
}

.chart-confidence-badge.low-confidence {
  background: rgba(244, 67, 54, 0.2);
  color: var(--color-error);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.results-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* ==========================================================================
   Loading Component Styles
   ========================================================================== */

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-2xl);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border-primary);
  opacity: 0;
  transform: scale(0.9);
  transition: all var(--transition-normal);
}

.loading-indicator.visible {
  opacity: 1;
  transform: scale(1);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top: 4px solid var(--color-success);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 2s linear infinite reverse;
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  text-align: center;
  margin: 0;
  animation: pulse 2s ease-in-out infinite;
}
/* ==========================================================================
   Error Component Styles
   ========================================================================== */

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-2xl);
  background: rgba(244, 67, 54, 0.1);
  border: 2px solid var(--color-error);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
  text-align: center;
  opacity: 0;
  transform: scale(0.9);
  transition: all var(--transition-normal);
}

.error-container.visible {
  opacity: 1;
  transform: scale(1);
}

.error-icon {
  font-size: var(--font-size-4xl);
}

.error-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-error);
  margin: 0;
}

.error-message {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  max-width: 400px;
  line-height: var(--line-height-relaxed);
}

.error-container.network-error {
  border-color: var(--color-warning);
  background: rgba(255, 167, 38, 0.1);
}

.error-container.network-error .error-title {
  color: var(--color-warning);
}

.error-container.model-error {
  border-color: var(--color-error);
  background: rgba(244, 67, 54, 0.1);
}

.error-container.model-error .error-title {
  color: var(--color-error);
}

.error-container.file-error {
  border-color: var(--color-warning);
  background: rgba(255, 167, 38, 0.1);
}

.error-container.file-error .error-title {
  color: var(--color-warning);
}

/* ==========================================================================
   Footer Component Styles
   ========================================================================== */

.footer {
  margin-top: auto;
  padding: var(--spacing-xl) var(--spacing-md);
  text-align: center;
  width: 100%;
}

.footer-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.footer-text a {
  color: var(--color-success);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  position: relative;
}

.footer-text a:hover,
.footer-text a:focus {
  color: var(--color-text-primary);
  text-decoration: underline;
}

.footer-text a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--color-success);
  transition: width var(--transition-normal);
}

.footer-text a:hover::after {
  width: 100%;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.hidden {
  display: none !important;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

/* ==========================================================================
   Focus Management
   ========================================================================== */

/* Enhanced focus styles for accessibility */
*:focus {
  outline: 3px solid var(--color-focus);
  outline-offset: 3px;
  border-radius: var(--radius-sm);
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 3px solid var(--color-focus);
  outline-offset: 3px;
  border-radius: var(--radius-sm);
  box-shadow: 0 0 0 6px rgba(0, 255, 191, 0.2);
  transition: all var(--transition-fast);
}

/* High contrast focus for interactive elements */
.btn:focus-visible,
.upload-label:focus-within {
  outline: 3px solid var(--color-focus);
  outline-offset: 4px;
  box-shadow: 0 0 0 8px rgba(0, 255, 191, 0.25);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .header__title {
    font-size: var(--font-size-2xl);
    padding: var(--spacing-md) var(--spacing-lg);
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .header__subtitle {
    font-size: var(--font-size-base);
  }

  .upload-label {
    min-width: unset;
    width: 100%;
    max-width: 400px;
    padding: var(--spacing-lg) var(--spacing-xl);
  }

  .upload-text {
    font-size: var(--font-size-base);
  }

  .upload-hint {
    font-size: var(--font-size-xs);
  }

  .preview-image {
    width: min(280px, 90vw);
    max-height: 280px;
  }

  .btn {
    width: 100%;
    max-width: 300px;
    padding: var(--spacing-lg) var(--spacing-xl);
  }

  .predict-button {
    min-width: unset;
  }

  .reset-button,
  .retry-button {
    min-width: unset;
  }

  .results-container {
    padding: var(--spacing-lg);
  }

  .prediction-header {
    font-size: var(--font-size-base);
  }

  .results-actions {
    flex-direction: column;
    align-items: center;
  }

  .loading-indicator {
    padding: var(--spacing-xl);
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
  }

  .loading-text {
    font-size: var(--font-size-base);
  }

  .error-container {
    padding: var(--spacing-xl);
    margin: 0 var(--spacing-md);
  }

  .footer {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .footer-text {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .header {
    margin-bottom: var(--spacing-xl);
  }

  .header__title {
    font-size: var(--font-size-xl);
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .header__subtitle {
    font-size: var(--font-size-sm);
  }

  .upload-label {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .upload-label::before {
    font-size: var(--font-size-xl);
  }

  .preview-image {
    width: min(240px, 90vw);
    max-height: 240px;
  }

  .preview-image:hover {
    transform: scale(1.02);
  }

  .btn {
    font-size: var(--font-size-base);
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .results-container {
    padding: var(--spacing-md);
  }

  .results-title {
    font-size: var(--font-size-lg);
  }

  .prediction-item {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .accuracy-chart {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
  }

  .chart-title {
    font-size: var(--font-size-lg);
  }

  .chart-row {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .chart-label {
    width: 100%;
  }

  .chart-accuracy {
    width: 100%;
    justify-content: space-between;
  }

  .chart-percentage {
    font-size: var(--font-size-base);
  }

  .loading-indicator {
    padding: var(--spacing-lg);
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
  }

  .loading-spinner::after {
    border-width: 2px;
  }

  .error-container {
    padding: var(--spacing-lg);
  }

  .error-icon {
    font-size: var(--font-size-3xl);
  }

  .error-title {
    font-size: var(--font-size-lg);
  }

  .error-message {
    font-size: var(--font-size-sm);
  }

  .footer {
    padding: var(--spacing-md);
  }
}

/* ==========================================================================
   Enhanced Mobile Responsiveness
   ========================================================================== */

@media (max-width: 640px) {
  body {
    padding: var(--spacing-md);
  }

  .main {
    gap: var(--spacing-xl);
    padding-top: var(--spacing-md);
  }

  .header__title {
    font-size: var(--font-size-2xl);
    padding: var(--spacing-lg) var(--spacing-xl);
  }

  .upload-label {
    min-width: unset;
    width: 100%;
    padding: var(--spacing-xl);
    min-height: 160px;
  }

  .upload-text {
    font-size: var(--font-size-lg);
  }

  .btn {
    width: 100%;
    max-width: 280px;
    min-height: 52px;
    font-size: var(--font-size-base);
  }

  .results-container {
    padding: var(--spacing-lg);
  }

  .prediction-item {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 56px;
  }

  .upload-label {
    min-height: 180px;
  }

  .prediction-item {
    padding: var(--spacing-lg);
  }

  /* Disable hover effects on touch devices */
  .header__title:hover,
  .upload-label:hover,
  .btn:hover,
  .prediction-item:hover,
  .preview-image:hover {
    transform: none;
  }
}

/* ==========================================================================
   Reduced Motion Support
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .header__title::before,
  .btn::before,
  .upload-label::after,
  .preview-image::before {
    display: none !important;
  }
}
