/* ==========================================================================
   Loading Component Styles
   ========================================================================== */

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xl);
  padding: var(--spacing-3xl);
  background: var(--gradient-card);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(16px);
  box-shadow: var(--shadow-2xl);
  border: 2px solid var(--color-border-secondary);
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  transition: all var(--transition-slow);
  position: relative;
  overflow: hidden;
}

.loading-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0.3;
  pointer-events: none;
}

.loading-indicator.visible {
  opacity: 1;
  transform: scale(1) translateY(0);
  animation: loadingAppear 0.8s ease-out;
}

.loading-spinner {
  width: 64px;
  height: 64px;
  border: 5px solid rgba(255, 255, 255, 0.15);
  border-top: 5px solid var(--color-primary);
  border-right: 5px solid var(--color-accent);
  border-radius: 50%;
  animation: spinEnhanced 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  position: relative;
  filter: drop-shadow(0 4px 8px rgba(0, 107, 93, 0.3));
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 3px solid transparent;
  border-top: 3px solid var(--color-success);
  border-left: 3px solid var(--color-secondary);
  border-radius: 50%;
  animation: spinEnhanced 2.5s cubic-bezier(0.4, 0, 0.2, 1) infinite reverse;
  opacity: 0.7;
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  text-align: center;
  margin: 0;
  animation: pulse 2s ease-in-out infinite;
}

/* Loading states */
.loading-model .loading-text::after {
  content: ' 🧠';
}

.loading-prediction .loading-text::after {
  content: ' 🔍';
}

.loading-image .loading-text::after {
  content: ' 📸';
}

/* Skeleton loading for results */
.skeleton-loader {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
}

.skeleton-item {
  height: 60px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
  background-size: 200px 100%;
  border-radius: var(--radius-lg);
  animation: shimmer 1.5s infinite;
}

.skeleton-item:nth-child(1) { animation-delay: 0s; }
.skeleton-item:nth-child(2) { animation-delay: 0.2s; }
.skeleton-item:nth-child(3) { animation-delay: 0.4s; }

/* Progress bar */
.loading-progress {
  width: 100%;
  max-width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-top: var(--spacing-md);
}

.loading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success), var(--color-primary));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  position: relative;
}

.loading-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

/* Dots loading animation */
.loading-dots {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.loading-dot {
  width: 8px;
  height: 8px;
  background: var(--color-success);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

/* ==========================================================================
   Enhanced Loading Animations
   ========================================================================== */

@keyframes loadingAppear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(30px);
  }
  50% {
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spinEnhanced {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ==========================================================================
   Loading Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .loading-indicator {
    padding: var(--spacing-xl);
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
  }
  
  .loading-text {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .loading-indicator {
    padding: var(--spacing-lg);
  }
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
  }
  
  .loading-spinner::after {
    border-width: 2px;
  }
}
