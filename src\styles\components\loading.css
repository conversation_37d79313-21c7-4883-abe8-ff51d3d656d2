/* ==========================================================================
   Loading Component Styles
   ========================================================================== */

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-2xl);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border-primary);
  opacity: 0;
  transform: scale(0.9);
  transition: all var(--transition-normal);
}

.loading-indicator.visible {
  opacity: 1;
  transform: scale(1);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top: 4px solid var(--color-success);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 2s linear infinite reverse;
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  text-align: center;
  margin: 0;
  animation: pulse 2s ease-in-out infinite;
}

/* Loading states */
.loading-model .loading-text::after {
  content: ' 🧠';
}

.loading-prediction .loading-text::after {
  content: ' 🔍';
}

.loading-image .loading-text::after {
  content: ' 📸';
}

/* Skeleton loading for results */
.skeleton-loader {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
}

.skeleton-item {
  height: 60px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
  background-size: 200px 100%;
  border-radius: var(--radius-lg);
  animation: shimmer 1.5s infinite;
}

.skeleton-item:nth-child(1) { animation-delay: 0s; }
.skeleton-item:nth-child(2) { animation-delay: 0.2s; }
.skeleton-item:nth-child(3) { animation-delay: 0.4s; }

/* Progress bar */
.loading-progress {
  width: 100%;
  max-width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-top: var(--spacing-md);
}

.loading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success), var(--color-primary));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  position: relative;
}

.loading-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

/* Dots loading animation */
.loading-dots {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.loading-dot {
  width: 8px;
  height: 8px;
  background: var(--color-success);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

/* ==========================================================================
   Loading Animations
   ========================================================================== */

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ==========================================================================
   Loading Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
  .loading-indicator {
    padding: var(--spacing-xl);
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
  }
  
  .loading-text {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .loading-indicator {
    padding: var(--spacing-lg);
  }
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
  }
  
  .loading-spinner::after {
    border-width: 2px;
  }
}
