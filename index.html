<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="AI-powered seaweed health classification system using machine learning to analyze seaweed condition from images">
  <meta name="keywords" content="seaweed, health, classification, AI, machine learning, TensorFlow">
  <meta name="author" content="Seaweed Health Team">

  <!-- Cache busting headers -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">

  <title>🌿 Seaweed Health Classifier - Medical Monitor</title>
  
  <!-- Preconnect to external domains for better performance -->
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  <link rel="preconnect" href="https://teachablemachine.withgoogle.com">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Chart.js for line charts -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <!-- Styles -->
  <link rel="stylesheet" href="src/styles/main.css">
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="src/assets/images/favicon.svg">
</head>
<body>
  <!-- Compact Grid Layout -->
  <div class="app-container">
    <!-- Header Section -->
    <header class="header-compact">
      <h1 class="header__title-compact">
        <span class="header__icon">🌿</span>
        Seaweed Condition Classifier
      </h1>
    </header>

    <!-- Main Grid Container -->
    <main class="main-grid">
      <!-- Left Panel: Upload & Preview -->
      <section class="left-panel">
        <!-- Upload Section -->
        <div class="upload-section-compact">
          <div class="upload-container-compact">
            <label for="fileInput" class="upload-label-compact">
              <span class="upload-text">Choose Image</span>
              <span class="upload-hint">Select seaweed image</span>
            </label>
            <input
              type="file"
              accept="image/*"
              id="fileInput"
              class="upload-input"
              aria-label="Upload seaweed image for classification"
            >
          </div>
        </div>

        <!-- Preview Section -->
        <div class="preview-section-compact">
          <div class="preview-container-compact">
            <img
              id="image-preview"
              class="preview-image-compact"
              alt="Uploaded seaweed image preview"
              style="display: none;"
            >
          </div>
        </div>

        <!-- Action Section -->
        <div class="action-section-compact">
          <button
            id="predict-button"
            class="predict-button-compact"
            type="button"
            disabled
            aria-label="Start seaweed health analysis"
          >
            <span class="button-text">Analyze Health</span>
            <span class="button-loader" style="display: none;">Analyzing...</span>
          </button>
        </div>

        <!-- Results Section -->
        <div class="results-section-compact">
          <div id="results-container" class="results-container-compact" style="display: none;">
            <!-- Chart Section -->
            <div id="accuracy-chart" class="accuracy-chart-compact">
              <div class="chart-header-compact">
                <h3 class="chart-title-compact">
                  Accuracy and Accuracy Test per Epoch
                </h3>
              </div>
              <div id="accuracy-chart-content" class="chart-content-compact">
                <canvas id="accuracy-line-chart" width="300" height="150"></canvas>
              </div>
            </div>

            <!-- Predictions List -->
            <div id="predictions-list" class="predictions-list-compact"></div>

            <!-- Reset Button -->
            <div class="results-actions-compact">
              <button id="reset-button" class="reset-button-compact" type="button">
                Analyze Another
              </button>
            </div>
          </div>
        </div>

        <!-- Loading Section -->
        <div class="loading-section-compact">
          <div id="loading-indicator" class="loading-indicator-compact">
            <div class="loading-spinner-compact"></div>
            <p class="loading-text-compact">Loading AI model...</p>
          </div>
        </div>

        <!-- Error Section -->
        <div class="error-section-compact">
          <div id="error-container" class="error-container-compact" style="display: none;">
            <div class="error-icon-compact">⚠️</div>
            <h3 class="error-title-compact">Something went wrong</h3>
            <p id="error-message" class="error-message-compact"></p>
            <button id="retry-button" class="retry-button-compact" type="button">
              Try Again
            </button>
          </div>
        </div>
      </section>
    </main>

    <!-- Compact Footer -->
    <footer class="footer-compact">
      <p class="footer-text-compact">
        Powered by <a href="https://teachablemachine.withgoogle.com/" target="_blank" rel="noopener">Teachable Machine</a>
        & <a href="https://www.tensorflow.org/js" target="_blank" rel="noopener">TensorFlow.js</a>
      </p>
    </footer>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.9.0"></script>
  <script src="https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8.4/dist/teachablemachine-image.min.js"></script>
  
  <!-- Application Scripts -->
  <script src="app.js?v=15&t=1734847700"></script>
</body>
</html>
