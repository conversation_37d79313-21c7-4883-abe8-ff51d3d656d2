/**
 * Seaweed Health Model Testing Application
 * Professional JavaScript implementation with Chart.js visualization
 */

class ModelTestingApp {
  constructor() {
    this.isTraining = false;
    this.currentEpoch = 0;
    this.maxEpochs = 100;
    this.trainingStartTime = null;
    this.trainingInterval = null;
    this.charts = {};

    // Seaweed condition configurations
    this.conditions = {
      healthy: {
        name: 'Healthy',
        color: '#00ffbf',
        baseAccuracy: 0.95,
        variance: 0.03
      },
      noise: {
        name: 'Noise',
        color: '#ff6b6b',
        baseAccuracy: 0.82,
        variance: 0.08
      },
      iceIce: {
        name: 'Ice-Ice',
        color: '#4ecdc4',
        baseAccuracy: 0.91,
        variance: 0.05
      },
      sirenSiren: {
        name: '<PERSON><PERSON>-<PERSON><PERSON>',
        color: '#45b7d1',
        baseAccuracy: 0.87,
        variance: 0.06
      },
      combined: {
        name: 'Ice-Ice & Siren-Siren',
        color: '#f9ca24',
        baseAccuracy: 0.79,
        variance: 0.09
      }
    };

    // Training data storage
    this.trainingData = {
      epochs: [],
      accuracy: {
        healthy: [],
        noise: [],
        iceIce: [],
        sirenSiren: [],
        combined: []
      },
      loss: {
        training: [],
        validation: []
      },
      validation: {
        healthy: [],
        noise: [],
        iceIce: [],
        sirenSiren: [],
        combined: []
      }
    };

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.initializeCharts();
    this.updateUI();
  }

  setupEventListeners() {
    // Control buttons
    document.getElementById('start-training').addEventListener('click', () => this.startTraining());
    document.getElementById('stop-training').addEventListener('click', () => this.stopTraining());
    document.getElementById('reset-training').addEventListener('click', () => this.resetTraining());

    // Parameter controls
    document.getElementById('epochs').addEventListener('change', (e) => {
      this.maxEpochs = parseInt(e.target.value);
    });

    // Chart controls
    document.querySelectorAll('.chart-control').forEach(control => {
      control.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        this.handleChartAction(action, e.target);
      });
    });
  }

  initializeCharts() {
    this.initAccuracyChart();
    this.initLossChart();
    this.initValidationChart();
  }

  initAccuracyChart() {
    const ctx = document.getElementById('accuracy-chart').getContext('2d');

    this.charts.accuracy = new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: Object.keys(this.conditions).map(key => ({
          label: this.conditions[key].name,
          data: [],
          borderColor: this.conditions[key].color,
          backgroundColor: this.conditions[key].color + '20',
          borderWidth: 3,
          fill: false,
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 6,
          pointBackgroundColor: this.conditions[key].color,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2
        }))
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(26, 26, 46, 0.95)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true,
            callbacks: {
              label: function(context) {
                return `${context.dataset.label}: ${(context.parsed.y * 100).toFixed(2)}%`;
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Epoch',
              color: '#ffffff',
              font: {
                size: 14,
                weight: 'bold'
              }
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Accuracy',
              color: '#ffffff',
              font: {
                size: 14,
                weight: 'bold'
              }
            },
            min: 0,
            max: 1,
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)',
              callback: function(value) {
                return (value * 100).toFixed(0) + '%';
              }
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        },
        animation: {
          duration: 750,
          easing: 'easeInOutQuart'
        }
      }
    });
  }

  initLossChart() {
    const ctx = document.getElementById('loss-chart').getContext('2d');

    this.charts.loss = new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: 'Training Loss',
            data: [],
            borderColor: '#ff6b6b',
            backgroundColor: '#ff6b6b20',
            borderWidth: 2,
            fill: false,
            tension: 0.4
          },
          {
            label: 'Validation Loss',
            data: [],
            borderColor: '#45b7d1',
            backgroundColor: '#45b7d120',
            borderWidth: 2,
            fill: false,
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top',
            labels: {
              color: '#ffffff',
              usePointStyle: true,
              padding: 20
            }
          },
          tooltip: {
            backgroundColor: 'rgba(26, 26, 46, 0.95)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 8
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Epoch',
              color: '#ffffff'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Loss',
              color: '#ffffff'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        animation: {
          duration: 500
        }
      }
    });
  }

  initValidationChart() {
    const ctx = document.getElementById('validation-chart').getContext('2d');

    this.charts.validation = new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: Object.keys(this.conditions).map(key => ({
          label: this.conditions[key].name,
          data: [],
          borderColor: this.conditions[key].color,
          backgroundColor: this.conditions[key].color + '20',
          borderWidth: 2,
          fill: false,
          tension: 0.4
        }))
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top',
            labels: {
              color: '#ffffff',
              usePointStyle: true,
              padding: 15,
              font: {
                size: 11
              }
            }
          },
          tooltip: {
            backgroundColor: 'rgba(26, 26, 46, 0.95)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 8,
            callbacks: {
              label: function(context) {
                return `${context.dataset.label}: ${(context.parsed.y * 100).toFixed(2)}%`;
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Epoch',
              color: '#ffffff'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Validation Accuracy',
              color: '#ffffff'
            },
            min: 0,
            max: 1,
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)',
              callback: function(value) {
                return (value * 100).toFixed(0) + '%';
              }
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        animation: {
          duration: 500
        }
      }
    });
  }

  startTraining() {
    if (this.isTraining) return;

    this.isTraining = true;
    this.trainingStartTime = Date.now();
    this.currentEpoch = 0;

    // Update UI
    this.updateTrainingButtons();
    this.updateStatus('Training');

    // Start training simulation
    this.trainingInterval = setInterval(() => {
      this.simulateEpoch();
      this.currentEpoch++;

      if (this.currentEpoch >= this.maxEpochs) {
        this.stopTraining();
      }
    }, 200); // 200ms per epoch for smooth animation
  }

  stopTraining() {
    if (!this.isTraining) return;

    this.isTraining = false;

    if (this.trainingInterval) {
      clearInterval(this.trainingInterval);
      this.trainingInterval = null;
    }

    this.updateTrainingButtons();
    this.updateStatus('Completed');
  }

  resetTraining() {
    this.stopTraining();

    // Reset data
    this.currentEpoch = 0;
    this.trainingData = {
      epochs: [],
      accuracy: {
        healthy: [],
        noise: [],
        iceIce: [],
        sirenSiren: [],
        combined: []
      },
      loss: {
        training: [],
        validation: []
      },
      validation: {
        healthy: [],
        noise: [],
        iceIce: [],
        sirenSiren: [],
        combined: []
      }
    };

    // Reset charts
    Object.values(this.charts).forEach(chart => {
      chart.data.labels = [];
      chart.data.datasets.forEach(dataset => {
        dataset.data = [];
      });
      chart.update();
    });

    // Reset UI
    this.updateUI();
    this.updateStatus('Ready');
    this.updateProgress(0);
  }

  simulateEpoch() {
    const epoch = this.currentEpoch + 1;
    this.trainingData.epochs.push(epoch);

    // Simulate accuracy for each condition
    Object.keys(this.conditions).forEach(key => {
      const condition = this.conditions[key];
      const progress = epoch / this.maxEpochs;

      // Simulate learning curve with some randomness
      const learningFactor = 1 - Math.exp(-progress * 3);
      const randomVariation = (Math.random() - 0.5) * condition.variance;
      const accuracy = Math.min(1, Math.max(0,
        condition.baseAccuracy * learningFactor + randomVariation
      ));

      this.trainingData.accuracy[key].push(accuracy);

      // Validation accuracy (slightly lower than training)
      const validationAccuracy = Math.min(1, Math.max(0,
        accuracy - 0.02 + (Math.random() - 0.5) * 0.03
      ));
      this.trainingData.validation[key].push(validationAccuracy);
    });

    // Simulate loss
    const trainingLoss = Math.max(0.01, 2.5 * Math.exp(-epoch * 0.05) + (Math.random() - 0.5) * 0.1);
    const validationLoss = Math.max(0.01, trainingLoss + 0.1 + (Math.random() - 0.5) * 0.05);

    this.trainingData.loss.training.push(trainingLoss);
    this.trainingData.loss.validation.push(validationLoss);

    // Update charts
    this.updateCharts();

    // Update UI metrics
    this.updateUI();

    // Update progress
    const progress = (epoch / this.maxEpochs) * 100;
    this.updateProgress(progress);
  }

  updateCharts() {
    // Update accuracy chart
    this.charts.accuracy.data.labels = this.trainingData.epochs;
    Object.keys(this.conditions).forEach((key, index) => {
      this.charts.accuracy.data.datasets[index].data = this.trainingData.accuracy[key];
    });
    this.charts.accuracy.update('none');

    // Update loss chart
    this.charts.loss.data.labels = this.trainingData.epochs;
    this.charts.loss.data.datasets[0].data = this.trainingData.loss.training;
    this.charts.loss.data.datasets[1].data = this.trainingData.loss.validation;
    this.charts.loss.update('none');

    // Update validation chart
    this.charts.validation.data.labels = this.trainingData.epochs;
    Object.keys(this.conditions).forEach((key, index) => {
      this.charts.validation.data.datasets[index].data = this.trainingData.validation[key];
    });
    this.charts.validation.update('none');
  }

  updateUI() {
    // Update current epoch
    document.getElementById('current-epoch').textContent = this.currentEpoch;

    // Update best accuracy
    let bestAccuracy = 0;
    Object.keys(this.conditions).forEach(key => {
      const accuracies = this.trainingData.accuracy[key];
      if (accuracies.length > 0) {
        const maxAccuracy = Math.max(...accuracies);
        bestAccuracy = Math.max(bestAccuracy, maxAccuracy);
      }
    });
    document.getElementById('best-accuracy').textContent = (bestAccuracy * 100).toFixed(2) + '%';

    // Update current loss
    const losses = this.trainingData.loss.training;
    if (losses.length > 0) {
      const currentLoss = losses[losses.length - 1];
      document.getElementById('current-loss').textContent = currentLoss.toFixed(3);
    }

    // Update training time
    if (this.trainingStartTime) {
      const elapsed = Date.now() - this.trainingStartTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      document.getElementById('training-time').textContent =
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Update result cards
    this.updateResultCards();
  }

  updateResultCards() {
    Object.keys(this.conditions).forEach(key => {
      const accuracies = this.trainingData.accuracy[key];
      const validationAccuracies = this.trainingData.validation[key];

      if (accuracies.length > 0) {
        const finalAccuracy = accuracies[accuracies.length - 1];
        const finalValidation = validationAccuracies[validationAccuracies.length - 1];

        // Calculate precision and recall (simulated)
        const precision = Math.min(1, finalAccuracy + (Math.random() - 0.5) * 0.05);
        const recall = Math.min(1, finalAccuracy + (Math.random() - 0.5) * 0.05);

        // Update DOM elements
        const conditionKey = key === 'iceIce' ? 'ice-ice' :
                           key === 'sirenSiren' ? 'siren-siren' : key;

        const accuracyElement = document.getElementById(`${conditionKey}-accuracy`);
        const precisionElement = document.getElementById(`${conditionKey}-precision`);
        const recallElement = document.getElementById(`${conditionKey}-recall`);

        if (accuracyElement) accuracyElement.textContent = (finalAccuracy * 100).toFixed(2) + '%';
        if (precisionElement) precisionElement.textContent = (precision * 100).toFixed(2) + '%';
        if (recallElement) recallElement.textContent = (recall * 100).toFixed(2) + '%';
      }
    });
  }

  updateTrainingButtons() {
    const startBtn = document.getElementById('start-training');
    const stopBtn = document.getElementById('stop-training');
    const resetBtn = document.getElementById('reset-training');

    if (this.isTraining) {
      startBtn.disabled = true;
      startBtn.querySelector('.btn__text').style.display = 'none';
      startBtn.querySelector('.btn__loader').style.display = 'flex';
      stopBtn.disabled = false;
      resetBtn.disabled = true;
    } else {
      startBtn.disabled = false;
      startBtn.querySelector('.btn__text').style.display = 'block';
      startBtn.querySelector('.btn__loader').style.display = 'none';
      stopBtn.disabled = true;
      resetBtn.disabled = false;
    }
  }

  updateStatus(status) {
    document.getElementById('training-status').textContent = status;
  }

  updateProgress(percentage) {
    const progressFill = document.getElementById('progress-fill');
    const progressPercentage = document.getElementById('progress-percentage');
    const progressText = document.getElementById('progress-text');

    progressFill.style.width = percentage + '%';
    progressPercentage.textContent = Math.round(percentage) + '%';

    if (percentage === 0) {
      progressText.textContent = 'Ready to start training';
    } else if (percentage === 100) {
      progressText.textContent = 'Training completed successfully';
    } else {
      progressText.textContent = `Training in progress - Epoch ${this.currentEpoch}/${this.maxEpochs}`;
    }
  }

  handleChartAction(action, element) {
    switch (action) {
      case 'download':
        this.downloadChart(element);
        break;
      case 'fullscreen':
        this.toggleFullscreen(element);
        break;
    }
  }

  downloadChart(element) {
    const chartCard = element.closest('.chart-card');
    const canvas = chartCard.querySelector('canvas');

    if (canvas) {
      const link = document.createElement('a');
      link.download = 'seaweed-model-chart.png';
      link.href = canvas.toDataURL();
      link.click();
    }
  }

  toggleFullscreen(element) {
    const chartCard = element.closest('.chart-card');

    if (chartCard.requestFullscreen) {
      chartCard.requestFullscreen();
    } else if (chartCard.webkitRequestFullscreen) {
      chartCard.webkitRequestFullscreen();
    } else if (chartCard.msRequestFullscreen) {
      chartCard.msRequestFullscreen();
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ModelTestingApp();
});

// Add some utility functions for enhanced UX
document.addEventListener('DOMContentLoaded', () => {
  // Add fade-in animation to cards
  const cards = document.querySelectorAll('.chart-card, .metric-card, .result-card');
  cards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';

    setTimeout(() => {
      card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
      card.style.opacity = '1';
      card.style.transform = 'translateY(0)';
    }, index * 100);
  });

  // Add hover effects to interactive elements
  const interactiveElements = document.querySelectorAll('.btn, .chart-control, .metric-card, .result-card');
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      element.style.transform = 'translateY(-2px)';
    });

    element.addEventListener('mouseleave', () => {
      element.style.transform = 'translateY(0)';
    });
  });
});
        labels: [],
        datasets: Object.keys(this.conditions).map(key => ({
          label: this.conditions[key].name,
          data: [],
          borderColor: this.conditions[key].color,
          backgroundColor: this.conditions[key].color + '20',
          borderWidth: 2,
          fill: false,
          tension: 0.4
        }))
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top',
            labels: {
              color: '#ffffff',
              usePointStyle: true,
              padding: 15,
              font: {
                size: 11
              }
            }
          },
          tooltip: {
            backgroundColor: 'rgba(26, 26, 46, 0.95)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 8,
            callbacks: {
              label: function(context) {
                return `${context.dataset.label}: ${(context.parsed.y * 100).toFixed(2)}%`;
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Epoch',
              color: '#ffffff'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Validation Accuracy',
              color: '#ffffff'
            },
            min: 0,
            max: 1,
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)',
              callback: function(value) {
                return (value * 100).toFixed(0) + '%';
              }
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        animation: {
          duration: 500
        }
      }
    });
  }
