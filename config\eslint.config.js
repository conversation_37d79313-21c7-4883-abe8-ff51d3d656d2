/**
 * ESLint Configuration
 * Seaweed Health Classifier
 */

module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: false
  },
  
  extends: [
    'eslint:recommended'
  ],
  
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'script'
  },
  
  globals: {
    // TensorFlow.js globals
    tf: 'readonly',
    tmImage: 'readonly',
    
    // Application globals
    AppConfig: 'readonly',
    Utils: 'readonly',
    ModelHandler: 'readonly',
    UIController: 'readonly',
    Environment: 'readonly',
    SeaweedClassifierApp: 'readonly'
  },
  
  rules: {
    // Error Prevention
    'no-console': ['warn', { allow: ['warn', 'error'] }],
    'no-debugger': 'error',
    'no-alert': 'error',
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    
    // Best Practices
    'eqeqeq': ['error', 'always'],
    'no-var': 'error',
    'prefer-const': 'error',
    'prefer-arrow-callback': 'error',
    'arrow-spacing': 'error',
    'no-duplicate-imports': 'error',
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    
    // Code Style
    'indent': ['error', 2],
    'quotes': ['error', 'single', { avoidEscape: true }],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    'space-before-blocks': 'error',
    'keyword-spacing': 'error',
    'space-infix-ops': 'error',
    'no-trailing-spaces': 'error',
    'eol-last': 'error',
    
    // Function Rules
    'func-style': ['error', 'declaration', { allowArrowFunctions: true }],
    'no-unused-expressions': 'error',
    'consistent-return': 'error',
    
    // Async/Promise Rules
    'no-async-promise-executor': 'error',
    'no-await-in-loop': 'warn',
    'prefer-promise-reject-errors': 'error',
    
    // Security
    'no-new-wrappers': 'error',
    'no-throw-literal': 'error'
  },
  
  overrides: [
    {
      files: ['tests/**/*.js'],
      env: {
        jest: true
      },
      globals: {
        expect: 'readonly',
        test: 'readonly',
        describe: 'readonly',
        beforeEach: 'readonly',
        afterEach: 'readonly',
        beforeAll: 'readonly',
        afterAll: 'readonly'
      }
    },
    
    {
      files: ['config/**/*.js'],
      env: {
        node: true,
        browser: false
      }
    }
  ]
};
